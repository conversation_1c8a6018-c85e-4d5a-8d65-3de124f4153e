<div class="ai-chatbot-container" [class.minimized]="isMinimized">
  <!-- Chatbot Header -->
  <div class="chatbot-header">
    <div class="header-content">
      <i class="fas fa-robot"></i>
      <span class="title">AI Legal Assistant</span>
      <div class="header-actions">
        <button class="btn-icon" (click)="toggleMinimize()" [title]="isMinimized ? 'Expand' : 'Minimize'">
          <i class="fas" [class.fa-chevron-up]="isMinimized" [class.fa-chevron-down]="!isMinimized"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Chatbot Content -->
  <div class="chatbot-content" *ngIf="!isMinimized">
    <div class="chatbot-layout">
      <!-- Conversations Sidebar -->
      <div class="conversations-sidebar" *ngIf="showConversationList">
        <div class="sidebar-header">
          <h4>Conversations</h4>
          <button class="btn btn-primary btn-sm" (click)="startNewConversation()" [disabled]="isLoading">
            <i class="fas fa-plus"></i>
            New Chat
          </button>
        </div>

        <div class="conversations-list">
          <div *ngIf="isLoading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
            Loading conversations...
          </div>

          <div *ngIf="!isLoading && conversations.length === 0" class="empty-state">
            <i class="fas fa-comments"></i>
            <p>No conversations yet</p>
            <button class="btn btn-outline-primary btn-sm" (click)="startNewConversation()">
              Start your first chat
            </button>
          </div>

          <div
            *ngFor="let conversation of conversations"
            class="conversation-item"
            [class.active]="currentConversation?.id === conversation.id"
            (click)="selectConversation(conversation)"
          >
            <div class="conversation-header">
              <span class="conversation-title">{{ conversation.title }}</span>
              <div class="conversation-actions">
                <span *ngIf="conversation.unread_count > 0" class="unread-badge">
                  {{ conversation.unread_count }}
                </span>
                <button
                  class="btn-icon btn-archive"
                  (click)="archiveConversation(conversation); $event.stopPropagation()"
                  title="Archive conversation"
                >
                  <i class="fas fa-archive"></i>
                </button>
              </div>
            </div>
            <div class="conversation-meta">
              <span class="message-count">{{ conversation.message_count }} messages</span>
              <span class="last-activity">{{ formatTimestamp(conversation.last_activity) }}</span>
            </div>
            <div *ngIf="conversation.has_document_context" class="document-indicator">
              <i class="fas fa-file-contract"></i>
              Document analysis available
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Area -->
      <div class="chat-area">
        <!-- No Conversation Selected -->
        <div *ngIf="!currentConversation" class="no-conversation-state">
          <div class="welcome-content">
            <i class="fas fa-robot fa-3x"></i>
            <h3>Welcome to AI Legal Assistant</h3>
            <p>I'm here to help you analyze contracts, identify legal issues, and provide recommendations.</p>
            <button class="btn btn-primary" (click)="startNewConversation()">
              <i class="fas fa-plus"></i>
              Start New Conversation
            </button>
          </div>
        </div>

        <!-- Active Conversation -->
        <div *ngIf="currentConversation" class="active-conversation">
          <!-- Messages Container -->
          <div class="messages-container" #messagesContainer>
            <div *ngFor="let message of messages" class="message-wrapper">
              <div class="message" [ngClass]="getMessageTypeClass(message)">
                <div class="message-avatar">
                  <i class="fas" [class]="getMessageIcon(message)"></i>
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender-name">{{ message.sender === 'user' ? 'You' : 'AI Assistant' }}</span>
                    <span class="message-time">{{ formatTimestamp(message.timestamp) }}</span>
                  </div>
                  <div class="message-body">
                    <div class="message-text" [innerHTML]="message.content"></div>

                    <!-- Referenced Entities -->
                    <div *ngIf="message.referenced_entities && message.referenced_entities.length > 0" class="referenced-entities">
                      <div class="entity-tag" *ngFor="let entity of message.referenced_entities">
                        <i class="fas fa-tag"></i>
                        {{ entity.type }}: {{ entity.name }}
                      </div>
                    </div>
                  </div>

                  <!-- AI Message Actions -->
                  <div *ngIf="message.sender === 'ai'" class="message-actions">
                    <button
                      class="btn-icon"
                      (click)="showFeedback(message.id!)"
                      title="Provide feedback"
                    >
                      <i class="fas fa-thumbs-up"></i>
                    </button>
                    <span *ngIf="message.is_helpful === true" class="feedback-indicator helpful">
                      <i class="fas fa-thumbs-up"></i>
                      Helpful
                    </span>
                    <span *ngIf="message.is_helpful === false" class="feedback-indicator not-helpful">
                      <i class="fas fa-thumbs-down"></i>
                      Not helpful
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Typing Indicator -->
            <div *ngIf="isTyping" class="typing-indicator">
              <div class="message ai-message">
                <div class="message-avatar">
                  <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                  <div class="typing-animation">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Message Input -->
          <div class="message-input-container">
            <div class="input-wrapper">
              <textarea
                #messageInput
                [(ngModel)]="newMessage"
                (keydown)="onKeyPress($event)"
                placeholder="Ask me about your contract, legal terms, or request analysis..."
                class="message-input"
                rows="1"
                [disabled]="isSending"
              ></textarea>
              <button
                class="send-button"
                (click)="sendMessage()"
                [disabled]="!newMessage.trim() || isSending"
              >
                <i class="fas" [class.fa-paper-plane]="!isSending" [class.fa-spinner]="isSending" [class.fa-spin]="isSending"></i>
              </button>
            </div>
            <div class="input-hints">
              <span class="hint">Press Enter to send, Shift+Enter for new line</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Feedback Modal -->
<div class="modal-overlay" *ngIf="showFeedbackModal" (click)="closeFeedbackModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h4>Provide Feedback</h4>
      <button class="btn-close" (click)="closeFeedbackModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <p>Was this response helpful?</p>
      <div class="feedback-buttons">
        <button class="btn btn-success" (click)="submitFeedback(true)">
          <i class="fas fa-thumbs-up"></i>
          Yes, helpful
        </button>
        <button class="btn btn-danger" (click)="submitFeedback(false)">
          <i class="fas fa-thumbs-down"></i>
          Not helpful
        </button>
      </div>
      <div class="feedback-text-area">
        <label for="feedbackText">Additional feedback (optional):</label>
        <textarea
          id="feedbackText"
          [(ngModel)]="feedbackText"
          placeholder="Tell us how we can improve..."
          rows="3"
        ></textarea>
      </div>
    </div>
  </div>
</div>