<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AiChatbotService;
use App\Models\AiConversation;
use App\Models\ChatMessage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class AiChatbotController extends Controller
{
    protected $aiChatbotService;

    public function __construct(AiChatbotService $aiChatbotService)
    {
        $this->aiChatbotService = $aiChatbotService;
        $this->middleware('auth:sanctum');
    }

    /**
     * Start a new conversation
     */
    public function startConversation(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'document_analysis_id' => 'nullable|integer|exists:document_analyses,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $documentAnalysisId = $request->input('document_analysis_id');

            // Verify user owns the document analysis if provided
            if ($documentAnalysisId) {
                $analysis = \App\Models\DocumentAnalysis::where('id', $documentAnalysisId)
                    ->where('user_id', $user->id)
                    ->first();

                if (!$analysis) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Document analysis not found or access denied'
                    ], 404);
                }
            }

            $conversation = $this->aiChatbotService->startConversation($user, $documentAnalysisId);

            return response()->json([
                'success' => true,
                'message' => 'Conversation started successfully',
                'data' => [
                    'conversation' => $this->aiChatbotService->getConversationSummary($conversation),
                    'welcome_message' => $conversation->chatMessages()->first()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to start AI conversation', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to start conversation'
            ], 500);
        }
    }

    /**
     * Send a message in a conversation
     */
    public function sendMessage(Request $request, int $conversationId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'message' => 'required|string|max:5000',
                'message_type' => 'nullable|string|in:text,question,clarification'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $conversation = AiConversation::where('id', $conversationId)
                ->where('user_id', $user->id)
                ->where('status', 'active')
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found or access denied'
                ], 404);
            }

            $message = $request->input('message');
            $messageType = $request->input('message_type', 'text');

            $userMessage = $this->aiChatbotService->sendMessage($conversation, $message, $messageType);

            // Get the AI response (should be the latest message from AI)
            $aiResponse = $conversation->chatMessages()
                ->where('sender_type', 'ai')
                ->latest()
                ->first();

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => [
                    'user_message' => [
                        'id' => $userMessage->id,
                        'content' => $userMessage->message_content,
                        'type' => $userMessage->message_type,
                        'timestamp' => $userMessage->created_at,
                        'sender' => 'user'
                    ],
                    'ai_response' => $aiResponse ? [
                        'id' => $aiResponse->id,
                        'content' => $aiResponse->message_content,
                        'type' => $aiResponse->message_type,
                        'timestamp' => $aiResponse->created_at,
                        'sender' => 'ai',
                        'referenced_entities' => $aiResponse->referenced_entities,
                        'metadata' => $aiResponse->message_metadata
                    ] : null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send message', [
                'user_id' => Auth::id(),
                'conversation_id' => $conversationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send message'
            ], 500);
        }
    }

    /**
     * Get conversation messages
     */
    public function getMessages(Request $request, int $conversationId): JsonResponse
    {
        try {
            $user = Auth::user();
            $conversation = AiConversation::where('id', $conversationId)
                ->where('user_id', $user->id)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found or access denied'
                ], 404);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 50);

            $messages = $conversation->chatMessages()
                ->orderBy('created_at', 'asc')
                ->paginate($limit, ['*'], 'page', $page);

            $formattedMessages = $messages->getCollection()->map(function ($message) {
                return [
                    'id' => $message->id,
                    'content' => $message->message_content,
                    'type' => $message->message_type,
                    'sender' => $message->sender_type,
                    'timestamp' => $message->created_at,
                    'is_read' => $message->isRead(),
                    'referenced_entities' => $message->referenced_entities,
                    'metadata' => $message->message_metadata,
                    'is_helpful' => $message->is_helpful,
                    'user_feedback' => $message->user_feedback
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'messages' => $formattedMessages,
                    'pagination' => [
                        'current_page' => $messages->currentPage(),
                        'last_page' => $messages->lastPage(),
                        'per_page' => $messages->perPage(),
                        'total' => $messages->total()
                    ],
                    'conversation' => $this->aiChatbotService->getConversationSummary($conversation)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get conversation messages', [
                'user_id' => Auth::id(),
                'conversation_id' => $conversationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve messages'
            ], 500);
        }
    }

    /**
     * Get user's conversations
     */
    public function getConversations(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $status = $request->input('status', 'active');

            $conversations = $this->aiChatbotService->getUserConversations($user, $status);

            return response()->json([
                'success' => true,
                'data' => [
                    'conversations' => $conversations
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get user conversations', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve conversations'
            ], 500);
        }
    }

    /**
     * Mark conversation as read
     */
    public function markAsRead(int $conversationId): JsonResponse
    {
        try {
            $user = Auth::user();
            $conversation = AiConversation::where('id', $conversationId)
                ->where('user_id', $user->id)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found or access denied'
                ], 404);
            }

            $markedCount = $this->aiChatbotService->markAsRead($conversation);

            return response()->json([
                'success' => true,
                'message' => 'Conversation marked as read',
                'data' => [
                    'marked_messages_count' => $markedCount
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to mark conversation as read', [
                'user_id' => Auth::id(),
                'conversation_id' => $conversationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to mark conversation as read'
            ], 500);
        }
    }

    /**
     * Archive conversation
     */
    public function archiveConversation(int $conversationId): JsonResponse
    {
        try {
            $user = Auth::user();
            $conversation = AiConversation::where('id', $conversationId)
                ->where('user_id', $user->id)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found or access denied'
                ], 404);
            }

            $archived = $this->aiChatbotService->archiveConversation($conversation);

            return response()->json([
                'success' => $archived,
                'message' => $archived ? 'Conversation archived successfully' : 'Failed to archive conversation'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to archive conversation', [
                'user_id' => Auth::id(),
                'conversation_id' => $conversationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to archive conversation'
            ], 500);
        }
    }

    /**
     * Provide feedback on AI message
     */
    public function provideFeedback(Request $request, int $messageId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'is_helpful' => 'required|boolean',
                'feedback' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $message = ChatMessage::whereHas('conversation', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->where('id', $messageId)
              ->where('sender_type', 'ai')
              ->first();

            if (!$message) {
                return response()->json([
                    'success' => false,
                    'message' => 'Message not found or access denied'
                ], 404);
            }

            $isHelpful = $request->input('is_helpful');
            $feedback = $request->input('feedback');

            if ($isHelpful) {
                $message->markAsHelpful($feedback);
            } else {
                $message->markAsUnhelpful($feedback);
            }

            return response()->json([
                'success' => true,
                'message' => 'Feedback recorded successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to record message feedback', [
                'user_id' => Auth::id(),
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to record feedback'
            ], 500);
        }
    }
}
