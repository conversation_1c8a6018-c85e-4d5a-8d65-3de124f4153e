<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Smalot\PdfParser\Parser as PdfParser;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Element\Text;
use PhpOffice\PhpWord\Element\TextRun;
use Exception;

class DocumentParsingService
{
    protected $pdfParser;

    public function __construct()
    {
        $this->pdfParser = new PdfParser();
    }

    /**
     * Parse document and extract content, structure, and entities
     */
    public function parseDocument(string $filePath): array
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        Log::info("Parsing document", [
            'file_path' => $filePath,
            'extension' => $extension
        ]);

        try {
            switch ($extension) {
                case 'pdf':
                    return $this->parsePdfDocument($filePath);
                case 'docx':
                case 'doc':
                    return $this->parseWordDocument($filePath);
                default:
                    throw new Exception("Unsupported document format: {$extension}");
            }
        } catch (Exception $e) {
            Log::error("Document parsing failed", [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);

            // Return basic structure with error information
            return [
                'text' => "Error: Unable to parse document content. " . $e->getMessage(),
                'structure' => [
                    'sections' => [],
                    'total_pages' => 0,
                    'parsing_error' => true
                ],
                'entities' => [
                    'parties' => [],
                    'dates' => [],
                    'amounts' => [],
                    'legal_terms' => []
                ]
            ];
        }
    }

    /**
     * Parse PDF document
     */
    protected function parsePdfDocument(string $filePath): array
    {
        try {
            $pdf = $this->pdfParser->parseFile($filePath);
            $text = $pdf->getText();
            $pages = $pdf->getPages();

            // Extract structure
            $structure = [
                'sections' => $this->extractSections($text),
                'total_pages' => count($pages),
                'page_contents' => []
            ];

            // Extract page-by-page content for location references
            foreach ($pages as $pageNum => $page) {
                $structure['page_contents'][$pageNum + 1] = [
                    'text' => $page->getText(),
                    'word_count' => str_word_count($page->getText())
                ];
            }

            // Extract legal entities
            $entities = $this->extractLegalEntities($text);

            return [
                'text' => $text,
                'structure' => $structure,
                'entities' => $entities
            ];

        } catch (Exception $e) {
            Log::error("PDF parsing failed", [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Parse Word document
     */
    protected function parseWordDocument(string $filePath): array
    {
        try {
            $phpWord = IOFactory::load($filePath);
            $text = '';
            $sections = [];

            foreach ($phpWord->getSections() as $sectionIndex => $section) {
                $sectionText = '';
                
                foreach ($section->getElements() as $element) {
                    $elementText = $this->extractTextFromElement($element);
                    $sectionText .= $elementText . "\n";
                    $text .= $elementText . "\n";
                }

                $sections[] = [
                    'index' => $sectionIndex + 1,
                    'text' => trim($sectionText),
                    'word_count' => str_word_count($sectionText)
                ];
            }

            // Extract structure
            $structure = [
                'sections' => $this->extractSections($text),
                'total_pages' => count($sections), // Approximate
                'word_sections' => $sections
            ];

            // Extract legal entities
            $entities = $this->extractLegalEntities($text);

            return [
                'text' => $text,
                'structure' => $structure,
                'entities' => $entities
            ];

        } catch (Exception $e) {
            Log::error("Word document parsing failed", [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Extract text from PhpWord element
     */
    protected function extractTextFromElement($element): string
    {
        $text = '';

        if ($element instanceof Text) {
            $text = $element->getText();
        } elseif ($element instanceof TextRun) {
            foreach ($element->getElements() as $textElement) {
                if ($textElement instanceof Text) {
                    $text .= $textElement->getText();
                }
            }
        } elseif (method_exists($element, 'getText')) {
            $text = $element->getText();
        } elseif (method_exists($element, 'getElements')) {
            foreach ($element->getElements() as $subElement) {
                $text .= $this->extractTextFromElement($subElement);
            }
        }

        return $text;
    }

    /**
     * Extract document sections and structure
     */
    protected function extractSections(string $text): array
    {
        $sections = [];
        
        // Common section patterns in legal documents
        $sectionPatterns = [
            '/^(\d+\.?\s+[A-Z][^.]*?)$/m',  // Numbered sections
            '/^([A-Z][A-Z\s]+)$/m',         // ALL CAPS headings
            '/^(ARTICLE\s+[IVX]+.*?)$/m',   // Article headings
            '/^(SECTION\s+\d+.*?)$/m',      // Section headings
            '/^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*):$/m' // Title case with colon
        ];

        foreach ($sectionPatterns as $pattern) {
            preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE);
            
            foreach ($matches[1] as $match) {
                $title = trim($match[0]);
                $position = $match[1];
                
                if (strlen($title) > 3 && strlen($title) < 100) {
                    $sections[] = [
                        'title' => $title,
                        'position' => $position,
                        'type' => $this->classifySectionType($title)
                    ];
                }
            }
        }

        // Sort by position and remove duplicates
        usort($sections, function($a, $b) {
            return $a['position'] <=> $b['position'];
        });

        return array_values(array_unique($sections, SORT_REGULAR));
    }

    /**
     * Classify section type based on title
     */
    protected function classifySectionType(string $title): string
    {
        $title = strtolower($title);
        
        $typePatterns = [
            'definitions' => ['definition', 'meaning', 'interpretation'],
            'obligations' => ['obligation', 'duties', 'responsibilities', 'shall'],
            'termination' => ['termination', 'expiry', 'end', 'conclusion'],
            'liability' => ['liability', 'damages', 'indemnity', 'limitation'],
            'dispute' => ['dispute', 'arbitration', 'resolution', 'governing law'],
            'confidentiality' => ['confidential', 'non-disclosure', 'proprietary'],
            'payment' => ['payment', 'fee', 'compensation', 'remuneration'],
            'intellectual_property' => ['intellectual property', 'copyright', 'trademark', 'patent']
        ];

        foreach ($typePatterns as $type => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($title, $keyword) !== false) {
                    return $type;
                }
            }
        }

        return 'general';
    }

    /**
     * Extract legal entities from text
     */
    protected function extractLegalEntities(string $text): array
    {
        return [
            'parties' => $this->extractParties($text),
            'dates' => $this->extractDates($text),
            'amounts' => $this->extractAmounts($text),
            'legal_terms' => $this->extractLegalTerms($text)
        ];
    }

    /**
     * Extract party names from contract
     */
    protected function extractParties(string $text): array
    {
        $parties = [];
        
        // Common party identification patterns
        $patterns = [
            '/(?:party|parties).*?["\']([^"\']+)["\']/',
            '/(?:between|among)\s+([A-Z][^,\n]+?)(?:\s+and\s+([A-Z][^,\n]+?))?/',
            '/([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*\(.*?["\']([^"\']+)["\'].*?\)/'
        ];

        foreach ($patterns as $pattern) {
            preg_match_all($pattern, $text, $matches);
            foreach ($matches[1] as $match) {
                $party = trim($match);
                if (strlen($party) > 2 && strlen($party) < 100) {
                    $parties[] = $party;
                }
            }
        }

        return array_unique($parties);
    }

    /**
     * Extract dates from contract
     */
    protected function extractDates(string $text): array
    {
        $dates = [];
        
        // Date patterns
        $patterns = [
            '/\b(\d{1,2}\/\d{1,2}\/\d{4})\b/',
            '/\b(\d{1,2}-\d{1,2}-\d{4})\b/',
            '/\b([A-Za-z]+\s+\d{1,2},?\s+\d{4})\b/',
            '/\b(\d{1,2}\s+[A-Za-z]+\s+\d{4})\b/'
        ];

        foreach ($patterns as $pattern) {
            preg_match_all($pattern, $text, $matches);
            $dates = array_merge($dates, $matches[1]);
        }

        return array_unique($dates);
    }

    /**
     * Extract monetary amounts from contract
     */
    protected function extractAmounts(string $text): array
    {
        $amounts = [];
        
        // Amount patterns
        $patterns = [
            '/\$[\d,]+(?:\.\d{2})?/',
            '/USD\s*[\d,]+(?:\.\d{2})?/',
            '/(?:dollars?|USD)\s*[\d,]+(?:\.\d{2})?/i'
        ];

        foreach ($patterns as $pattern) {
            preg_match_all($pattern, $text, $matches);
            $amounts = array_merge($amounts, $matches[0]);
        }

        return array_unique($amounts);
    }

    /**
     * Extract legal terms and concepts
     */
    protected function extractLegalTerms(string $text): array
    {
        $legalTerms = [
            'force majeure', 'indemnification', 'liquidated damages', 'specific performance',
            'breach of contract', 'material adverse change', 'intellectual property',
            'confidentiality', 'non-disclosure', 'governing law', 'jurisdiction',
            'arbitration', 'mediation', 'termination', 'assignment', 'novation'
        ];

        $foundTerms = [];
        $textLower = strtolower($text);

        foreach ($legalTerms as $term) {
            if (strpos($textLower, $term) !== false) {
                $foundTerms[] = $term;
            }
        }

        return $foundTerms;
    }
}
