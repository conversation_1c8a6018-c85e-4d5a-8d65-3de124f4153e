import { <PERSON><PERSON>nent, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { AiChatbotService, ChatMessage, AiConversation } from '../../services/ai-chatbot.service';

@Component({
  selector: 'app-ai-chatbot',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './ai-chatbot.component.html',
  styleUrls: ['./ai-chatbot.component.css']
})
export class AiChatbotComponent implements OnInit, OnDestroy {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  @Input() documentAnalysisId?: number;
  @Input() isMinimized: boolean = false;
  @Input() showConversationList: boolean = true;

  conversations: AiConversation[] = [];
  currentConversation: AiConversation | null = null;
  messages: ChatMessage[] = [];

  newMessage: string = '';
  isLoading: boolean = false;
  isSending: boolean = false;
  isTyping: boolean = false;

  showFeedbackModal: boolean = false;
  feedbackMessageId: number | null = null;
  feedbackText: string = '';

  private subscriptions: Subscription[] = [];

  constructor(private aiChatbotService: AiChatbotService) {}

  ngOnInit(): void {
    this.loadConversations();
    this.subscribeToUpdates();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private subscribeToUpdates(): void {
    // Subscribe to conversations updates
    this.subscriptions.push(
      this.aiChatbotService.conversations$.subscribe(conversations => {
        this.conversations = conversations;
      })
    );

    // Subscribe to current conversation updates
    this.subscriptions.push(
      this.aiChatbotService.currentConversation$.subscribe(conversation => {
        this.currentConversation = conversation;
        if (conversation) {
          this.markAsRead(conversation.id);
        }
      })
    );

    // Subscribe to messages updates
    this.subscriptions.push(
      this.aiChatbotService.messages$.subscribe(messages => {
        this.messages = messages;
        setTimeout(() => this.scrollToBottom(), 100);
      })
    );
  }

  loadConversations(): void {
    this.isLoading = true;
    this.aiChatbotService.loadConversations().subscribe({
      next: (conversations) => {
        this.isLoading = false;
        // If no current conversation and we have conversations, select the first one
        if (!this.currentConversation && conversations.length > 0) {
          this.selectConversation(conversations[0]);
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Failed to load conversations:', error);
      }
    });
  }

  startNewConversation(): void {
    this.isLoading = true;
    this.aiChatbotService.startConversation(this.documentAnalysisId).subscribe({
      next: (data) => {
        this.isLoading = false;
        this.selectConversation(data.conversation);
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Failed to start conversation:', error);
      }
    });
  }

  selectConversation(conversation: AiConversation): void {
    this.aiChatbotService.setCurrentConversation(conversation);
  }

  sendMessage(): void {
    if (!this.newMessage.trim() || !this.currentConversation || this.isSending) {
      return;
    }

    const message = this.newMessage.trim();
    this.newMessage = '';
    this.isSending = true;
    this.isTyping = true;

    this.aiChatbotService.sendMessage(this.currentConversation.id, message).subscribe({
      next: (data) => {
        this.isSending = false;
        this.isTyping = false;
        // Messages are automatically updated via subscription
      },
      error: (error) => {
        this.isSending = false;
        this.isTyping = false;
        console.error('Failed to send message:', error);
        // Optionally show error message to user
      }
    });
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  markAsRead(conversationId: number): void {
    this.aiChatbotService.markAsRead(conversationId).subscribe({
      next: () => {
        // Conversation marked as read
      },
      error: (error) => {
        console.error('Failed to mark conversation as read:', error);
      }
    });
  }

  archiveConversation(conversation: AiConversation): void {
    if (confirm('Are you sure you want to archive this conversation?')) {
      this.aiChatbotService.archiveConversation(conversation.id).subscribe({
        next: () => {
          if (this.currentConversation?.id === conversation.id) {
            this.currentConversation = null;
            this.messages = [];
          }
        },
        error: (error) => {
          console.error('Failed to archive conversation:', error);
        }
      });
    }
  }

  showFeedback(messageId: number): void {
    this.feedbackMessageId = messageId;
    this.showFeedbackModal = true;
  }

  submitFeedback(isHelpful: boolean): void {
    if (this.feedbackMessageId) {
      this.aiChatbotService.provideFeedback(this.feedbackMessageId, isHelpful, this.feedbackText).subscribe({
        next: () => {
          this.closeFeedbackModal();
        },
        error: (error) => {
          console.error('Failed to submit feedback:', error);
        }
      });
    }
  }

  closeFeedbackModal(): void {
    this.showFeedbackModal = false;
    this.feedbackMessageId = null;
    this.feedbackText = '';
  }

  private scrollToBottom(): void {
    if (this.messagesContainer) {
      const element = this.messagesContainer.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }

  getMessageIcon(message: ChatMessage): string {
    if (message.sender === 'user') {
      return 'fa-user';
    }

    switch (message.type) {
      case 'analysis_result':
        return 'fa-chart-line';
      case 'recommendation':
        return 'fa-lightbulb';
      case 'question':
        return 'fa-question-circle';
      case 'clarification':
        return 'fa-info-circle';
      default:
        return 'fa-robot';
    }
  }

  getMessageTypeClass(message: ChatMessage): string {
    const baseClass = message.sender === 'user' ? 'user-message' : 'ai-message';
    return `${baseClass} ${message.type}-message`;
  }

  formatTimestamp(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;

    return timestamp.toLocaleDateString();
  }

  toggleMinimize(): void {
    this.isMinimized = !this.isMinimized;
  }
}