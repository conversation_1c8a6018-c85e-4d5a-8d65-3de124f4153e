<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatMessage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'conversation_id',
        'sender_type',
        'message_content',
        'message_metadata',
        'message_type',
        'referenced_entities',
        'is_helpful',
        'user_feedback',
        'read_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'message_metadata' => 'array',
        'referenced_entities' => 'array',
        'is_helpful' => 'boolean',
        'read_at' => 'datetime',
    ];

    /**
     * Get the conversation that owns this message.
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(AiConversation::class, 'conversation_id');
    }

    /**
     * Scope to filter by sender type.
     */
    public function scopeBySender($query, $senderType)
    {
        return $query->where('sender_type', $senderType);
    }

    /**
     * Scope to filter by message type.
     */
    public function scopeByType($query, $messageType)
    {
        return $query->where('message_type', $messageType);
    }

    /**
     * Scope to get user messages.
     */
    public function scopeFromUser($query)
    {
        return $query->where('sender_type', 'user');
    }

    /**
     * Scope to get AI messages.
     */
    public function scopeFromAi($query)
    {
        return $query->where('sender_type', 'ai');
    }

    /**
     * Scope to get unread messages.
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope to get read messages.
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope to get helpful messages.
     */
    public function scopeHelpful($query)
    {
        return $query->where('is_helpful', true);
    }

    /**
     * Scope to get unhelpful messages.
     */
    public function scopeUnhelpful($query)
    {
        return $query->where('is_helpful', false);
    }

    /**
     * Mark the message as read.
     */
    public function markAsRead(): bool
    {
        if ($this->read_at) {
            return true; // Already read
        }

        return $this->update(['read_at' => now()]);
    }

    /**
     * Mark the message as helpful.
     */
    public function markAsHelpful(string $feedback = null): bool
    {
        return $this->update([
            'is_helpful' => true,
            'user_feedback' => $feedback,
        ]);
    }

    /**
     * Mark the message as unhelpful.
     */
    public function markAsUnhelpful(string $feedback = null): bool
    {
        return $this->update([
            'is_helpful' => false,
            'user_feedback' => $feedback,
        ]);
    }

    /**
     * Check if the message is from user.
     */
    public function isFromUser(): bool
    {
        return $this->sender_type === 'user';
    }

    /**
     * Check if the message is from AI.
     */
    public function isFromAi(): bool
    {
        return $this->sender_type === 'ai';
    }

    /**
     * Check if the message is read.
     */
    public function isRead(): bool
    {
        return $this->read_at !== null;
    }

    /**
     * Check if the message has been rated as helpful.
     */
    public function isHelpful(): bool
    {
        return $this->is_helpful === true;
    }

    /**
     * Check if the message has been rated as unhelpful.
     */
    public function isUnhelpful(): bool
    {
        return $this->is_helpful === false;
    }

    /**
     * Check if the message has user feedback.
     */
    public function hasFeedback(): bool
    {
        return !empty($this->user_feedback);
    }

    /**
     * Get the message content preview for display.
     */
    public function getPreviewAttribute(): string
    {
        $preview = substr($this->message_content, 0, 100);
        return strlen($this->message_content) > 100 ? $preview . '...' : $preview;
    }

    /**
     * Get the sender display name.
     */
    public function getSenderDisplayNameAttribute(): string
    {
        return $this->sender_type === 'user' ? 'You' : 'AI Assistant';
    }

    /**
     * Get the message type icon for UI display.
     */
    public function getTypeIconAttribute(): string
    {
        return match ($this->message_type) {
            'text' => 'fa-comment',
            'analysis_result' => 'fa-chart-line',
            'recommendation' => 'fa-lightbulb',
            'question' => 'fa-question-circle',
            'clarification' => 'fa-info-circle',
            default => 'fa-message',
        };
    }

    /**
     * Get the sender avatar for UI display.
     */
    public function getSenderAvatarAttribute(): string
    {
        if ($this->sender_type === 'user') {
            // Get user avatar from conversation
            $user = $this->conversation->user;
            return $user->avatar_url ?? '/assets/images/default-avatar.png';
        }

        return '/assets/images/ai-avatar.png';
    }

    /**
     * Get formatted timestamp for display.
     */
    public function getFormattedTimestampAttribute(): string
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    /**
     * Get relative timestamp for display.
     */
    public function getRelativeTimestampAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get referenced entities as formatted text.
     */
    public function getFormattedReferencesAttribute(): string
    {
        if (!$this->referenced_entities) {
            return '';
        }

        $references = [];
        foreach ($this->referenced_entities as $entity) {
            if (isset($entity['type']) && isset($entity['name'])) {
                $references[] = "{$entity['type']}: {$entity['name']}";
            }
        }

        return implode(', ', $references);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Update conversation's last activity when a message is created
        static::created(function ($message) {
            $message->conversation->updateLastActivity();
        });
    }
}
