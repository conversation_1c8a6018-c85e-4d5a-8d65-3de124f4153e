<?php

namespace App\Services;

use App\Models\AiConversation;
use App\Models\ChatMessage;
use App\Models\DocumentAnalysis;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Exception;

class AiChatbotService
{
    protected $openAiService;

    public function __construct(OpenAiService $openAiService)
    {
        $this->openAiService = $openAiService;
    }

    /**
     * Start a new conversation
     */
    public function startConversation(User $user, ?int $documentAnalysisId = null): AiConversation
    {
        Log::info("Starting new AI conversation", [
            'user_id' => $user->id,
            'document_analysis_id' => $documentAnalysisId
        ]);

        $conversation = AiConversation::create([
            'user_id' => $user->id,
            'document_analysis_id' => $documentAnalysisId,
            'status' => 'active',
            'last_activity_at' => now(),
            'context_data' => $this->buildInitialContext($user, $documentAnalysisId)
        ]);

        // Send welcome message
        $this->sendWelcomeMessage($conversation);

        return $conversation;
    }

    /**
     * Send a message in a conversation
     */
    public function sendMessage(AiConversation $conversation, string $message, string $messageType = 'text'): ChatMessage
    {
        Log::info("Sending user message", [
            'conversation_id' => $conversation->id,
            'message_type' => $messageType,
            'message_length' => strlen($message)
        ]);

        // Create user message
        $userMessage = ChatMessage::create([
            'conversation_id' => $conversation->id,
            'sender_type' => 'user',
            'message_content' => $message,
            'message_type' => $messageType,
            'message_metadata' => [
                'timestamp' => now()->toISOString(),
                'user_agent' => request()->header('User-Agent')
            ]
        ]);

        // Generate AI response
        $this->generateAiResponse($conversation, $message);

        return $userMessage;
    }

    /**
     * Generate AI response to user message
     */
    protected function generateAiResponse(AiConversation $conversation, string $userMessage): ChatMessage
    {
        try {
            // Build context for AI
            $context = $this->buildConversationContext($conversation);
            
            // Generate response using OpenAI
            $aiResponse = $this->openAiService->generateChatResponse($userMessage, $context);
            
            // Determine response type and extract metadata
            $responseData = $this->analyzeAiResponse($aiResponse, $context);
            
            // Create AI message
            $aiMessage = ChatMessage::create([
                'conversation_id' => $conversation->id,
                'sender_type' => 'ai',
                'message_content' => $responseData['content'],
                'message_type' => $responseData['type'],
                'message_metadata' => $responseData['metadata'],
                'referenced_entities' => $responseData['referenced_entities']
            ]);

            Log::info("AI response generated", [
                'conversation_id' => $conversation->id,
                'response_type' => $responseData['type'],
                'response_length' => strlen($responseData['content'])
            ]);

            return $aiMessage;

        } catch (Exception $e) {
            Log::error("Failed to generate AI response", [
                'conversation_id' => $conversation->id,
                'error' => $e->getMessage()
            ]);

            // Create fallback response
            return ChatMessage::create([
                'conversation_id' => $conversation->id,
                'sender_type' => 'ai',
                'message_content' => "I apologize, but I'm experiencing technical difficulties. Please try rephrasing your question or contact support if the issue persists.",
                'message_type' => 'text',
                'message_metadata' => [
                    'error' => true,
                    'error_message' => $e->getMessage()
                ]
            ]);
        }
    }

    /**
     * Build initial context for conversation
     */
    protected function buildInitialContext(User $user, ?int $documentAnalysisId): array
    {
        $context = [
            'user_preferences' => [
                'language' => 'en',
                'expertise_level' => 'general', // general, intermediate, expert
                'communication_style' => 'professional'
            ],
            'session_info' => [
                'started_at' => now()->toISOString(),
                'timezone' => config('app.timezone')
            ]
        ];

        // Add document context if available
        if ($documentAnalysisId) {
            $analysis = DocumentAnalysis::with(['legalIssues', 'aiRecommendations'])->find($documentAnalysisId);
            if ($analysis) {
                $context['document_context'] = [
                    'analysis_id' => $analysis->id,
                    'document_type' => $analysis->document_type,
                    'total_issues' => $analysis->total_issues_found,
                    'total_recommendations' => $analysis->total_recommendations,
                    'confidence_score' => $analysis->confidence_score,
                    'high_priority_issues' => $analysis->legalIssues()->whereIn('severity', ['high', 'critical'])->count(),
                    'pending_recommendations' => $analysis->aiRecommendations()->where('status', 'pending')->count()
                ];
            }
        }

        return $context;
    }

    /**
     * Build conversation context for AI
     */
    protected function buildConversationContext(AiConversation $conversation): array
    {
        $context = $conversation->context_data ?? [];

        // Add recent conversation history
        $recentMessages = $conversation->chatMessages()
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->reverse()
            ->map(function ($message) {
                return [
                    'sender_type' => $message->sender_type,
                    'content' => $message->message_content,
                    'type' => $message->message_type,
                    'timestamp' => $message->created_at->toISOString()
                ];
            })
            ->toArray();

        $context['conversation_history'] = $recentMessages;

        // Add document analysis details if available
        if ($conversation->document_analysis_id) {
            $analysis = $conversation->documentAnalysis;
            if ($analysis) {
                $context['document_analysis'] = [
                    'content_summary' => substr($analysis->content_extracted ?? '', 0, 1000),
                    'structure' => $analysis->document_structure,
                    'entities' => $analysis->legal_entities,
                    'issues' => $analysis->legalIssues()->with('aiRecommendations')->get()->map(function ($issue) {
                        return [
                            'type' => $issue->issue_type,
                            'severity' => $issue->severity,
                            'title' => $issue->title,
                            'description' => $issue->description,
                            'location' => $issue->location_in_document,
                            'recommendations_count' => $issue->aiRecommendations->count()
                        ];
                    })->toArray(),
                    'recommendations' => $analysis->aiRecommendations()->get()->map(function ($rec) {
                        return [
                            'type' => $rec->recommendation_type,
                            'priority' => $rec->priority,
                            'title' => $rec->title,
                            'description' => $rec->description,
                            'status' => $rec->status
                        ];
                    })->toArray()
                ];
            }
        }

        return $context;
    }

    /**
     * Analyze AI response and extract metadata
     */
    protected function analyzeAiResponse(string $response, array $context): array
    {
        // Determine message type based on content
        $messageType = 'text';
        $referencedEntities = [];
        $metadata = [
            'generated_at' => now()->toISOString(),
            'context_used' => !empty($context['document_analysis'])
        ];

        // Check if response references specific document elements
        if (!empty($context['document_analysis'])) {
            // Look for references to issues or recommendations
            foreach ($context['document_analysis']['issues'] as $issue) {
                if (stripos($response, $issue['title']) !== false || 
                    stripos($response, $issue['type']) !== false) {
                    $referencedEntities[] = [
                        'type' => 'legal_issue',
                        'id' => $issue['type'],
                        'name' => $issue['title']
                    ];
                    $messageType = 'analysis_result';
                }
            }

            foreach ($context['document_analysis']['recommendations'] as $rec) {
                if (stripos($response, $rec['title']) !== false || 
                    stripos($response, $rec['type']) !== false) {
                    $referencedEntities[] = [
                        'type' => 'recommendation',
                        'id' => $rec['type'],
                        'name' => $rec['title']
                    ];
                    $messageType = 'recommendation';
                }
            }
        }

        // Check for question patterns
        if (preg_match('/\?$/', trim($response))) {
            $messageType = 'clarification';
        }

        return [
            'content' => $response,
            'type' => $messageType,
            'metadata' => $metadata,
            'referenced_entities' => $referencedEntities
        ];
    }

    /**
     * Send welcome message to new conversation
     */
    protected function sendWelcomeMessage(AiConversation $conversation): ChatMessage
    {
        $welcomeMessage = "Hello! I'm your AI legal assistant. I'm here to help you understand your contracts, identify potential issues, and provide recommendations for improvement.";

        if ($conversation->document_analysis_id) {
            $analysis = $conversation->documentAnalysis;
            if ($analysis && $analysis->isCompleted()) {
                $welcomeMessage .= "\n\nI've already analyzed your document and found {$analysis->total_issues_found} potential issues and generated {$analysis->total_recommendations} recommendations. Feel free to ask me about any specific concerns or sections of your contract.";
            }
        }

        $welcomeMessage .= "\n\nWhat would you like to know about your contract?";

        return ChatMessage::create([
            'conversation_id' => $conversation->id,
            'sender_type' => 'ai',
            'message_content' => $welcomeMessage,
            'message_type' => 'text',
            'message_metadata' => [
                'is_welcome_message' => true,
                'generated_at' => now()->toISOString()
            ]
        ]);
    }

    /**
     * Get conversation summary
     */
    public function getConversationSummary(AiConversation $conversation): array
    {
        $messageCount = $conversation->chatMessages()->count();
        $userMessageCount = $conversation->chatMessages()->where('sender_type', 'user')->count();
        $aiMessageCount = $conversation->chatMessages()->where('sender_type', 'ai')->count();
        $unreadCount = $conversation->chatMessages()
            ->where('sender_type', 'ai')
            ->whereNull('read_at')
            ->count();

        return [
            'id' => $conversation->id,
            'title' => $conversation->display_title,
            'status' => $conversation->status,
            'message_count' => $messageCount,
            'user_messages' => $userMessageCount,
            'ai_messages' => $aiMessageCount,
            'unread_count' => $unreadCount,
            'last_activity' => $conversation->last_activity_at,
            'has_document_context' => !is_null($conversation->document_analysis_id),
            'created_at' => $conversation->created_at
        ];
    }

    /**
     * Mark conversation messages as read
     */
    public function markAsRead(AiConversation $conversation): int
    {
        return $conversation->markAllAsRead();
    }

    /**
     * Archive conversation
     */
    public function archiveConversation(AiConversation $conversation): bool
    {
        return $conversation->archive();
    }

    /**
     * Get user's conversations
     */
    public function getUserConversations(User $user, string $status = 'active'): array
    {
        $conversations = AiConversation::where('user_id', $user->id)
            ->where('status', $status)
            ->with(['documentAnalysis'])
            ->orderBy('last_activity_at', 'desc')
            ->get();

        return $conversations->map(function ($conversation) {
            return $this->getConversationSummary($conversation);
        })->toArray();
    }
}
