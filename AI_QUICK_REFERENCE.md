# AI Integration Quick Reference

## 🚀 Quick Start Commands

### Setup (One-time)
```bash
# Linux/Mac
chmod +x setup-ai-integration.sh
./setup-ai-integration.sh

# Windows
setup-ai-integration.bat
```

### Development
```bash
# Start all services
./start-dev.sh        # Linux/Mac
start-dev.bat          # Windows

# Or manually:
cd laravel-backend
php artisan serve &
php artisan queue:work &

cd ../src
ng serve
```

### Testing
```bash
# Run integration tests
php test-ai-integration.php

# Test API endpoints
curl -X GET "http://localhost:8000/api/ai/analysis/dashboard-summary" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔧 Configuration Files

### Backend (.env)
```env
OPENAI_API_KEY=your_key_here
OPENAI_MODEL=gpt-4o-mini
AI_ANALYSIS_ENABLED=true
AI_CHATBOT_ENABLED=true
QUEUE_CONNECTION=database
```

### Frontend (environment.ts)
```typescript
export const environment = {
  apiUrl: 'http://localhost:8000/api'
};
```

## 📡 API Endpoints

### Document Analysis
```
POST /api/ai/analysis/analyze
GET  /api/ai/analysis/dashboard-summary
GET  /api/ai/analysis/{id}
GET  /api/ai/analysis/{id}/issues
GET  /api/ai/analysis/{id}/recommendations
```

### Chatbot
```
GET  /api/ai/chat/conversations
POST /api/ai/chat/conversations
POST /api/ai/chat/conversations/{id}/messages
GET  /api/ai/chat/conversations/{id}/messages
```

## 🎨 Frontend Components

### Dashboard Widget
```typescript
// Already integrated in dashboard.component.html
<div class="analysis-widget-card">
  <!-- AI Document Analysis Widget -->
</div>
```

### Standalone Analysis Component
```typescript
import { DocumentAnalysisComponent } from './components/document-analysis/document-analysis.component';

// Use in template:
<app-document-analysis 
  [documentId]="contract.id" 
  [documentType]="'unsigned_contract'">
</app-document-analysis>
```

### Chatbot Component
```typescript
import { AiChatbotComponent } from './components/ai-chatbot/ai-chatbot.component';

// Use in template:
<app-ai-chatbot 
  [documentAnalysisId]="analysisId">
</app-ai-chatbot>
```

## 🔄 Queue Management

### Start Queue Worker
```bash
php artisan queue:work
php artisan queue:work --tries=3 --timeout=90
```

### Monitor Queue
```bash
php artisan queue:monitor
php artisan queue:failed
php artisan queue:retry all
```

### Clear Queue
```bash
php artisan queue:clear
php artisan queue:flush
```

## 🛠️ Troubleshooting

### Common Issues

**OpenAI API Errors**
```bash
# Check API key
php artisan tinker
>>> config('services.openai.api_key')

# Test connection
>>> app(\App\Services\OpenAiService::class)->isServiceAvailable()
```

**Queue Not Processing**
```bash
# Check queue status
php artisan queue:work --once

# Check failed jobs
php artisan queue:failed

# Restart queue worker
php artisan queue:restart
```

**Database Issues**
```bash
# Check migrations
php artisan migrate:status

# Run missing migrations
php artisan migrate

# Reset database (development only)
php artisan migrate:fresh --seed
```

**CORS Issues**
```bash
# Check CORS middleware
# Verify APP_FRONTEND_URL in .env
# Check browser network tab for CORS errors
```

### Debug Commands

```bash
# Check service status
php artisan tinker
>>> app(\App\Services\OpenAiService::class)->getServiceStatus()

# Test document analysis
>>> $service = app(\App\Services\AiDocumentAnalysisService::class)

# Check AI configuration
>>> config('ai')
```

## 📊 Monitoring

### Log Files
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# Queue worker logs
tail -f storage/logs/worker.log

# AI service logs (search for 'OpenAI' or 'AI')
grep -i "openai\|ai" storage/logs/laravel.log
```

### Performance Monitoring
```bash
# Check queue metrics
php artisan queue:monitor

# Database queries
php artisan telescope:install  # Optional
```

## 🔐 Security

### API Rate Limiting
```php
// In routes/api.php
Route::middleware(['throttle:ai'])->group(function () {
    // AI routes
});
```

### Input Validation
```php
// Always validate document uploads
$request->validate([
    'document' => 'required|file|max:10240|mimes:pdf,doc,docx'
]);
```

## 🎯 Customization

### AI Prompts
Edit `app/Services/OpenAiService.php`:
```php
protected function getSystemPrompt(): string
{
    return "Your custom legal analysis prompt...";
}
```

### Analysis Types
Add new analysis types in:
- `app/Models/DocumentAnalysis.php`
- `database/migrations/`
- Frontend interfaces

### UI Customization
- Dashboard widget: `src/app/components/dashboard/`
- Analysis component: `src/app/components/document-analysis/`
- Chatbot: `src/app/components/ai-chatbot/`

## 📚 Resources

- [OpenAI API Docs](https://platform.openai.com/docs)
- [Laravel Queue Docs](https://laravel.com/docs/queues)
- [Angular HTTP Guide](https://angular.io/guide/http)

## ⚡ Performance Tips

1. **Use Queue Workers**: Always run `php artisan queue:work` in production
2. **Cache Results**: Consider caching analysis results for large documents
3. **Optimize Prompts**: Keep AI prompts concise but specific
4. **Monitor Usage**: Track OpenAI API usage and costs
5. **Error Handling**: Implement proper fallbacks for AI service failures

## 🔄 Updates

### Update AI Models
```env
# In .env file
OPENAI_MODEL=gpt-4o-mini  # or latest model
```

### Update Dependencies
```bash
# Backend
composer update

# Frontend
npm update
```

### Database Updates
```bash
# Create new migration
php artisan make:migration add_ai_features

# Run migrations
php artisan migrate
```
