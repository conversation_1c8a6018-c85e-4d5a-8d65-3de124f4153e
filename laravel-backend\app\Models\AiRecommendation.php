<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AiRecommendation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'document_analysis_id',
        'legal_issue_id',
        'recommendation_type',
        'priority',
        'title',
        'description',
        'implementation_guide',
        'original_text',
        'suggested_text',
        'location_references',
        'legal_rationale',
        'status',
        'impact_score',
        'implemented_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'location_references' => 'array',
        'impact_score' => 'decimal:2',
        'implemented_at' => 'datetime',
    ];

    /**
     * Get the document analysis that owns this recommendation.
     */
    public function documentAnalysis(): BelongsTo
    {
        return $this->belongsTo(DocumentAnalysis::class);
    }

    /**
     * Get the legal issue associated with this recommendation.
     */
    public function legalIssue(): BelongsTo
    {
        return $this->belongsTo(LegalIssue::class);
    }

    /**
     * Scope to filter by priority.
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by recommendation type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('recommendation_type', $type);
    }

    /**
     * Scope to get pending recommendations.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get accepted recommendations.
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    /**
     * Scope to get implemented recommendations.
     */
    public function scopeImplemented($query)
    {
        return $query->where('status', 'implemented');
    }

    /**
     * Scope to get high priority recommendations.
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }

    /**
     * Accept the recommendation.
     */
    public function accept(): bool
    {
        return $this->update(['status' => 'accepted']);
    }

    /**
     * Reject the recommendation.
     */
    public function reject(): bool
    {
        return $this->update(['status' => 'rejected']);
    }

    /**
     * Mark the recommendation as implemented.
     */
    public function markAsImplemented(): bool
    {
        return $this->update([
            'status' => 'implemented',
            'implemented_at' => now(),
        ]);
    }

    /**
     * Get the priority color for UI display.
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'low' => '#10b981',      // green
            'medium' => '#f59e0b',   // yellow
            'high' => '#f97316',     // orange
            'urgent' => '#ef4444',   // red
            default => '#6b7280',    // gray
        };
    }

    /**
     * Get the priority icon for UI display.
     */
    public function getPriorityIconAttribute(): string
    {
        return match ($this->priority) {
            'low' => 'fa-arrow-down',
            'medium' => 'fa-minus',
            'high' => 'fa-arrow-up',
            'urgent' => 'fa-exclamation',
            default => 'fa-question',
        };
    }

    /**
     * Get the status color for UI display.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => '#f59e0b',    // yellow
            'accepted' => '#3b82f6',   // blue
            'rejected' => '#ef4444',   // red
            'implemented' => '#10b981', // green
            default => '#6b7280',      // gray
        };
    }

    /**
     * Get the status icon for UI display.
     */
    public function getStatusIconAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'fa-clock',
            'accepted' => 'fa-check',
            'rejected' => 'fa-times',
            'implemented' => 'fa-check-double',
            default => 'fa-question',
        };
    }

    /**
     * Get the recommendation type icon for UI display.
     */
    public function getTypeIconAttribute(): string
    {
        return match ($this->recommendation_type) {
            'clause_addition' => 'fa-plus-circle',
            'language_modification' => 'fa-edit',
            'risk_mitigation' => 'fa-shield-alt',
            'compliance_improvement' => 'fa-gavel',
            'structure_enhancement' => 'fa-sitemap',
            default => 'fa-lightbulb',
        };
    }

    /**
     * Check if the recommendation is urgent.
     */
    public function isUrgent(): bool
    {
        return $this->priority === 'urgent';
    }

    /**
     * Check if the recommendation is high priority.
     */
    public function isHighPriority(): bool
    {
        return in_array($this->priority, ['high', 'urgent']);
    }

    /**
     * Check if the recommendation is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the recommendation has been implemented.
     */
    public function isImplemented(): bool
    {
        return $this->status === 'implemented';
    }

    /**
     * Get formatted location references for display.
     */
    public function getFormattedLocationReferencesAttribute(): string
    {
        if (!$this->location_references) {
            return 'No specific location';
        }

        $references = $this->location_references;
        $parts = [];

        foreach ($references as $ref) {
            $refParts = [];
            
            if (isset($ref['section'])) {
                $refParts[] = "Section {$ref['section']}";
            }
            
            if (isset($ref['clause'])) {
                $refParts[] = "Clause {$ref['clause']}";
            }
            
            if (isset($ref['page'])) {
                $refParts[] = "Page {$ref['page']}";
            }
            
            if (!empty($refParts)) {
                $parts[] = implode(', ', $refParts);
            }
        }

        return implode('; ', $parts) ?: 'Document references';
    }
}
