import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface ChatMessage {
  id?: number;
  content: string;
  type: 'text' | 'analysis_result' | 'recommendation' | 'question' | 'clarification';
  sender: 'user' | 'ai';
  timestamp: Date;
  is_read?: boolean;
  referenced_entities?: any[];
  metadata?: any;
  is_helpful?: boolean;
  user_feedback?: string;
}

export interface AiConversation {
  id: number;
  title: string;
  status: 'active' | 'archived' | 'deleted';
  message_count: number;
  user_messages: number;
  ai_messages: number;
  unread_count: number;
  last_activity: Date;
  has_document_context: boolean;
  created_at: Date;
}

export interface DocumentAnalysis {
  id: number;
  document_type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  total_issues: number;
  total_recommendations: number;
  confidence_score: number;
  processing_time?: number;
  high_severity_issues: number;
  pending_recommendations: number;
  document_structure?: any;
  legal_entities?: any;
  analysis_metadata?: any;
  created_at: Date;
  updated_at: Date;
}

export interface LegalIssue {
  id: number;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  title: string;
  description: string;
  location: string;
  suggested_fix?: string;
  legal_reasoning?: string;
  confidence_score?: number;
  is_resolved: boolean;
  severity_color: string;
  severity_icon: string;
  category_icon: string;
  created_at: Date;
}

export interface AiRecommendation {
  id: number;
  type: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  implementation_guide?: string;
  original_text?: string;
  suggested_text?: string;
  location_references?: string;
  legal_rationale?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'implemented';
  impact_score?: number;
  priority_color: string;
  priority_icon: string;
  status_color: string;
  status_icon: string;
  type_icon: string;
  created_at: Date;
}

@Injectable({
  providedIn: 'root'
})
export class AiChatbotService {
  private apiUrl = `${environment.apiUrl}/ai`;
  private conversationsSubject = new BehaviorSubject<AiConversation[]>([]);
  private currentConversationSubject = new BehaviorSubject<AiConversation | null>(null);
  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);

  public conversations$ = this.conversationsSubject.asObservable();
  public currentConversation$ = this.currentConversationSubject.asObservable();
  public messages$ = this.messagesSubject.asObservable();

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Conversation Management
  startConversation(documentAnalysisId?: number): Observable<any> {
    const body = documentAnalysisId ? { document_analysis_id: documentAnalysisId } : {};

    return this.http.post(`${this.apiUrl}/chat/conversations`, body, { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            this.currentConversationSubject.next(response.data.conversation);
            this.loadConversations(); // Refresh conversations list
            return response.data;
          }
          throw new Error(response.message || 'Failed to start conversation');
        }),
        catchError(this.handleError)
      );
  }

  loadConversations(status: string = 'active'): Observable<AiConversation[]> {
    return this.http.get(`${this.apiUrl}/chat/conversations?status=${status}`, { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            const conversations = response.data.conversations.map((conv: any) => ({
              ...conv,
              last_activity: new Date(conv.last_activity),
              created_at: new Date(conv.created_at)
            }));
            this.conversationsSubject.next(conversations);
            return conversations;
          }
          throw new Error(response.message || 'Failed to load conversations');
        }),
        catchError(this.handleError)
      );
  }

  loadMessages(conversationId: number, page: number = 1, limit: number = 50): Observable<any> {
    return this.http.get(`${this.apiUrl}/chat/conversations/${conversationId}/messages?page=${page}&limit=${limit}`,
      { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            const messages = response.data.messages.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }));
            this.messagesSubject.next(messages);
            return response.data;
          }
          throw new Error(response.message || 'Failed to load messages');
        }),
        catchError(this.handleError)
      );
  }

  sendMessage(conversationId: number, message: string, messageType: string = 'text'): Observable<any> {
    const body = {
      message: message,
      message_type: messageType
    };

    return this.http.post(`${this.apiUrl}/chat/conversations/${conversationId}/messages`, body,
      { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            // Add new messages to current messages
            const currentMessages = this.messagesSubject.value;
            const newMessages = [
              ...currentMessages,
              {
                ...response.data.user_message,
                timestamp: new Date(response.data.user_message.timestamp)
              }
            ];

            if (response.data.ai_response) {
              newMessages.push({
                ...response.data.ai_response,
                timestamp: new Date(response.data.ai_response.timestamp)
              });
            }

            this.messagesSubject.next(newMessages);
            return response.data;
          }
          throw new Error(response.message || 'Failed to send message');
        }),
        catchError(this.handleError)
      );
  }

  markAsRead(conversationId: number): Observable<any> {
    return this.http.patch(`${this.apiUrl}/chat/conversations/${conversationId}/read`, {},
      { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            this.loadConversations(); // Refresh to update unread counts
            return response.data;
          }
          throw new Error(response.message || 'Failed to mark as read');
        }),
        catchError(this.handleError)
      );
  }

  archiveConversation(conversationId: number): Observable<any> {
    return this.http.patch(`${this.apiUrl}/chat/conversations/${conversationId}/archive`, {},
      { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            this.loadConversations(); // Refresh conversations list
            return response.data;
          }
          throw new Error(response.message || 'Failed to archive conversation');
        }),
        catchError(this.handleError)
      );
  }

  provideFeedback(messageId: number, isHelpful: boolean, feedback?: string): Observable<any> {
    const body = {
      is_helpful: isHelpful,
      feedback: feedback
    };

    return this.http.post(`${this.apiUrl}/chat/messages/${messageId}/feedback`, body,
      { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to provide feedback');
        }),
        catchError(this.handleError)
      );
  }

  // Document Analysis Methods
  analyzeDocument(documentId: number, documentType: string): Observable<any> {
    const body = {
      document_id: documentId,
      document_type: documentType
    };

    return this.http.post(`${this.apiUrl}/analysis/analyze`, body, { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to start document analysis');
        }),
        catchError(this.handleError)
      );
  }

  getAnalysis(analysisId: number): Observable<DocumentAnalysis> {
    return this.http.get(`${this.apiUrl}/analysis/${analysisId}`, { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            return {
              ...response.data.analysis,
              created_at: new Date(response.data.analysis.created_at),
              updated_at: new Date(response.data.analysis.updated_at)
            };
          }
          throw new Error(response.message || 'Failed to get analysis');
        }),
        catchError(this.handleError)
      );
  }

  getDocumentAnalysisSummary(documentId: number, documentType: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/analysis/summary?document_id=${documentId}&document_type=${documentType}`,
      { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            return response.data.summary;
          }
          throw new Error(response.message || 'Failed to get analysis summary');
        }),
        catchError(this.handleError)
      );
  }

  getAnalysisSummary(): Observable<any> {
    return this.http.get(`${this.apiUrl}/analysis/dashboard-summary`, { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to get analysis summary');
        }),
        catchError(this.handleError)
      );
  }

  getLegalIssues(analysisId: number): Observable<{ issues: LegalIssue[], summary: any }> {
    return this.http.get(`${this.apiUrl}/analysis/${analysisId}/issues`, { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            const issues = response.data.issues.map((issue: any) => ({
              ...issue,
              created_at: new Date(issue.created_at)
            }));
            return {
              issues: issues,
              summary: response.data.summary
            };
          }
          throw new Error(response.message || 'Failed to get legal issues');
        }),
        catchError(this.handleError)
      );
  }

  getRecommendations(analysisId: number): Observable<{ recommendations: AiRecommendation[], summary: any }> {
    return this.http.get(`${this.apiUrl}/analysis/${analysisId}/recommendations`, { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            const recommendations = response.data.recommendations.map((rec: any) => ({
              ...rec,
              created_at: new Date(rec.created_at)
            }));
            return {
              recommendations: recommendations,
              summary: response.data.summary
            };
          }
          throw new Error(response.message || 'Failed to get recommendations');
        }),
        catchError(this.handleError)
      );
  }

  updateRecommendationStatus(recommendationId: number, status: string): Observable<any> {
    const body = { status: status };

    return this.http.patch(`${this.apiUrl}/analysis/recommendations/${recommendationId}/status`, body,
      { headers: this.getHeaders() })
      .pipe(
        map((response: any) => {
          if (response.success) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to update recommendation status');
        }),
        catchError(this.handleError)
      );
  }

  // Utility Methods
  setCurrentConversation(conversation: AiConversation | null): void {
    this.currentConversationSubject.next(conversation);
    if (conversation) {
      this.loadMessages(conversation.id);
    } else {
      this.messagesSubject.next([]);
    }
  }

  getCurrentConversation(): AiConversation | null {
    return this.currentConversationSubject.value;
  }

  private handleError(error: any): Observable<never> {
    console.error('AI Chatbot Service Error:', error);
    let errorMessage = 'An unexpected error occurred';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
