<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AiConversation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'document_analysis_id',
        'conversation_title',
        'status',
        'context_data',
        'last_activity_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'context_data' => 'array',
        'last_activity_at' => 'datetime',
    ];

    /**
     * Get the user that owns this conversation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the document analysis associated with this conversation.
     */
    public function documentAnalysis(): BelongsTo
    {
        return $this->belongsTo(DocumentAnalysis::class);
    }

    /**
     * Get the chat messages for this conversation.
     */
    public function chatMessages(): HasMany
    {
        return $this->hasMany(ChatMessage::class, 'conversation_id');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get active conversations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get archived conversations.
     */
    public function scopeArchived($query)
    {
        return $query->where('status', 'archived');
    }

    /**
     * Scope to order by last activity.
     */
    public function scopeByLastActivity($query, $direction = 'desc')
    {
        return $query->orderBy('last_activity_at', $direction);
    }

    /**
     * Update the last activity timestamp.
     */
    public function updateLastActivity(): bool
    {
        return $this->update(['last_activity_at' => now()]);
    }

    /**
     * Archive the conversation.
     */
    public function archive(): bool
    {
        return $this->update(['status' => 'archived']);
    }

    /**
     * Restore the conversation from archive.
     */
    public function restore(): bool
    {
        return $this->update(['status' => 'active']);
    }

    /**
     * Soft delete the conversation.
     */
    public function softDelete(): bool
    {
        return $this->update(['status' => 'deleted']);
    }

    /**
     * Get the latest message in the conversation.
     */
    public function getLatestMessageAttribute()
    {
        return $this->chatMessages()->latest()->first();
    }

    /**
     * Get the message count for this conversation.
     */
    public function getMessageCountAttribute(): int
    {
        return $this->chatMessages()->count();
    }

    /**
     * Get unread message count for this conversation.
     */
    public function getUnreadMessageCountAttribute(): int
    {
        return $this->chatMessages()
            ->where('sender_type', 'ai')
            ->whereNull('read_at')
            ->count();
    }

    /**
     * Check if the conversation has unread messages.
     */
    public function hasUnreadMessages(): bool
    {
        return $this->unread_message_count > 0;
    }

    /**
     * Mark all messages as read.
     */
    public function markAllAsRead(): int
    {
        return $this->chatMessages()
            ->where('sender_type', 'ai')
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
    }

    /**
     * Get conversation title or generate one from context.
     */
    public function getDisplayTitleAttribute(): string
    {
        if ($this->conversation_title) {
            return $this->conversation_title;
        }

        // Generate title from document analysis if available
        if ($this->documentAnalysis && $this->documentAnalysis->document) {
            $document = $this->documentAnalysis->document;
            $documentName = $document->nom_contrat ?? $document->name ?? 'Document';
            return "Chat about {$documentName}";
        }

        // Generate title from first message
        $firstMessage = $this->chatMessages()->where('sender_type', 'user')->first();
        if ($firstMessage) {
            $preview = substr($firstMessage->message_content, 0, 50);
            return strlen($firstMessage->message_content) > 50 ? $preview . '...' : $preview;
        }

        return 'AI Conversation';
    }

    /**
     * Get conversation summary for display.
     */
    public function getSummaryAttribute(): string
    {
        $latestMessage = $this->latest_message;
        if (!$latestMessage) {
            return 'No messages yet';
        }

        $preview = substr($latestMessage->message_content, 0, 100);
        return strlen($latestMessage->message_content) > 100 ? $preview . '...' : $preview;
    }

    /**
     * Check if conversation is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if conversation is archived.
     */
    public function isArchived(): bool
    {
        return $this->status === 'archived';
    }

    /**
     * Check if conversation is deleted.
     */
    public function isDeleted(): bool
    {
        return $this->status === 'deleted';
    }

    /**
     * Get the time since last activity in human readable format.
     */
    public function getTimeSinceLastActivityAttribute(): string
    {
        if (!$this->last_activity_at) {
            return 'Never';
        }

        return $this->last_activity_at->diffForHumans();
    }
}
