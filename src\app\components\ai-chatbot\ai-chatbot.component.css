/* AI Chatbot Container */
.ai-chatbot-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 800px;
  max-width: 90vw;
  height: 600px;
  max-height: 80vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  transition: all 0.3s ease;
}

.ai-chatbot-container.minimized {
  height: 60px;
  width: 300px;
}

/* Header */
.chatbot-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  border-radius: 12px 12px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.header-content i {
  margin-right: 10px;
  font-size: 1.2em;
}

.title {
  font-weight: 600;
  font-size: 1.1em;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn-icon {
  background: none;
  border: none;
  color: white;
  padding: 5px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Content */
.chatbot-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chatbot-layout {
  display: flex;
  height: 100%;
}

/* Conversations Sidebar */
.conversations-sidebar {
  width: 280px;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h4 {
  margin: 0;
  font-size: 1em;
  color: #333;
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.conversation-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: 1px solid transparent;
}

.conversation-item:hover {
  background-color: #e9ecef;
}

.conversation-item.active {
  background-color: #e3f2fd;
  border-color: #2196f3;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.conversation-title {
  font-weight: 500;
  color: #333;
  font-size: 0.9em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.conversation-actions {
  display: flex;
  align-items: center;
  gap: 5px;
}

.unread-badge {
  background-color: #f44336;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 0.7em;
  font-weight: bold;
}

.btn-archive {
  opacity: 0;
  transition: opacity 0.2s;
}

.conversation-item:hover .btn-archive {
  opacity: 1;
}

.conversation-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.8em;
  color: #666;
}

.document-indicator {
  margin-top: 5px;
  font-size: 0.75em;
  color: #4caf50;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Loading and Empty States */
.loading-state, .empty-state {
  text-align: center;
  padding: 20px;
  color: #666;
}

.empty-state i {
  font-size: 2em;
  margin-bottom: 10px;
  color: #ccc;
}

/* Chat Area */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.no-conversation-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-content {
  text-align: center;
  max-width: 400px;
  padding: 20px;
}

.welcome-content i {
  color: #667eea;
  margin-bottom: 20px;
}

.welcome-content h3 {
  color: #333;
  margin-bottom: 15px;
}

.welcome-content p {
  color: #666;
  margin-bottom: 25px;
  line-height: 1.5;
}

.active-conversation {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #fafafa;
}

.message-wrapper {
  margin-bottom: 20px;
}

.message {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.user-message {
  margin-left: auto;
  flex-direction: row-reverse;
}

.user-message .message-content {
  background-color: #667eea;
  color: white;
}

.ai-message .message-content {
  background-color: white;
  border: 1px solid #e0e0e0;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #667eea;
  color: white;
}

.ai-message .message-avatar {
  background-color: #4caf50;
  color: white;
}

.message-content {
  flex: 1;
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sender-name {
  font-weight: 600;
  font-size: 0.85em;
}

.user-message .sender-name {
  color: rgba(255, 255, 255, 0.9);
}

.ai-message .sender-name {
  color: #333;
}

.message-time {
  font-size: 0.75em;
  opacity: 0.7;
}

.message-body {
  line-height: 1.5;
}

.message-text {
  margin-bottom: 8px;
}

/* Referenced Entities */
.referenced-entities {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.entity-tag {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-message .entity-tag {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Message Actions */
.message-actions {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.feedback-indicator {
  font-size: 0.8em;
  display: flex;
  align-items: center;
  gap: 4px;
}

.feedback-indicator.helpful {
  color: #4caf50;
}

.feedback-indicator.not-helpful {
  color: #f44336;
}

/* Typing Indicator */
.typing-indicator {
  margin-bottom: 20px;
}

.typing-animation {
  display: flex;
  gap: 4px;
  padding: 8px 0;
}

.typing-animation span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ccc;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-animation span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Message Input */
.message-input-container {
  border-top: 1px solid #e0e0e0;
  background-color: white;
  padding: 15px 20px;
}

.input-wrapper {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 10px 15px;
  resize: none;
  font-family: inherit;
  font-size: 0.9em;
  line-height: 1.4;
  max-height: 100px;
  overflow-y: auto;
}

.message-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover:not(:disabled) {
  background-color: #5a6fd8;
}

.send-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.input-hints {
  margin-top: 5px;
  text-align: center;
}

.hint {
  font-size: 0.75em;
  color: #999;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h4 {
  margin: 0;
  color: #333;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.2em;
  cursor: pointer;
  color: #666;
  padding: 5px;
}

.modal-body {
  padding: 20px;
}

.feedback-buttons {
  display: flex;
  gap: 10px;
  margin: 15px 0;
}

.feedback-text-area {
  margin-top: 15px;
}

.feedback-text-area label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.feedback-text-area textarea {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  font-family: inherit;
  resize: vertical;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.9em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
}

.btn-primary {
  background-color: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #5a6fd8;
}

.btn-outline-primary {
  background-color: transparent;
  color: #667eea;
  border: 1px solid #667eea;
}

.btn-outline-primary:hover:not(:disabled) {
  background-color: #667eea;
  color: white;
}

.btn-success {
  background-color: #4caf50;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #45a049;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #da190b;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8em;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-chatbot-container {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    bottom: 0;
    right: 0;
    border-radius: 0;
  }

  .ai-chatbot-container.minimized {
    height: 60px;
    width: 100%;
    border-radius: 0;
  }

  .chatbot-header {
    border-radius: 0;
  }

  .conversations-sidebar {
    width: 100%;
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    background: white;
    display: none;
  }

  .conversations-sidebar.show {
    display: flex;
  }

  .message {
    max-width: 90%;
  }
}