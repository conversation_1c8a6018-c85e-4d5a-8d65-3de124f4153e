<?php

namespace App\Jobs;

use App\Services\AiDocumentAnalysisService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AnalyzeDocumentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $documentId;
    protected $documentType;
    protected $userId;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(int $documentId, string $documentType, int $userId)
    {
        $this->documentId = $documentId;
        $this->documentType = $documentType;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(AiDocumentAnalysisService $analysisService): void
    {
        Log::info('Starting document analysis job', [
            'document_id' => $this->documentId,
            'document_type' => $this->documentType,
            'user_id' => $this->userId,
            'job_id' => $this->job->getJobId()
        ]);

        try {
            $analysis = $analysisService->analyzeDocument(
                $this->documentId,
                $this->documentType,
                $this->userId
            );

            Log::info('Document analysis job completed successfully', [
                'analysis_id' => $analysis->id,
                'issues_found' => $analysis->total_issues_found,
                'recommendations' => $analysis->total_recommendations,
                'job_id' => $this->job->getJobId()
            ]);

            // Optionally, you can dispatch events or notifications here
            // event(new DocumentAnalysisCompleted($analysis));

        } catch (\Exception $e) {
            Log::error('Document analysis job failed', [
                'document_id' => $this->documentId,
                'document_type' => $this->documentType,
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'job_id' => $this->job->getJobId()
            ]);

            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Document analysis job failed permanently', [
            'document_id' => $this->documentId,
            'document_type' => $this->documentType,
            'user_id' => $this->userId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Optionally, you can send notifications to the user about the failure
        // or update the analysis record to mark it as failed
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array<int, string>
     */
    public function tags(): array
    {
        return [
            'document_analysis',
            'user:' . $this->userId,
            'document:' . $this->documentType . ':' . $this->documentId
        ];
    }
}
