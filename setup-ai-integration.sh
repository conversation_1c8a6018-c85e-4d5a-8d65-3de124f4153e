#!/bin/bash

# AI Integration Setup Script
# This script automates the setup process for AI document analysis and chatbot features

set -e

echo "🚀 Starting AI Integration Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "laravel-backend/artisan" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Check Prerequisites
print_step "1. Checking Prerequisites..."

# Check PHP version
if ! command -v php &> /dev/null; then
    print_error "PHP is not installed"
    exit 1
fi

PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
if [ "$(printf '%s\n' "8.2" "$PHP_VERSION" | sort -V | head -n1)" != "8.2" ]; then
    print_error "PHP 8.2 or higher is required. Current version: $PHP_VERSION"
    exit 1
fi

print_status "PHP version: $PHP_VERSION ✓"

# Check Composer
if ! command -v composer &> /dev/null; then
    print_error "Composer is not installed"
    exit 1
fi

print_status "Composer is installed ✓"

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version: $(node -v) ✓"

# Step 2: Install Backend Dependencies
print_step "2. Installing Backend Dependencies..."

cd laravel-backend

# Install PHP dependencies
print_status "Installing PHP dependencies..."
composer install --no-dev --optimize-autoloader

# Step 3: Database Setup
print_step "3. Setting up Database..."

# Check if .env exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Please copy .env.example to .env and configure it."
    cp .env.example .env
    print_status "Created .env file from .env.example"
fi

# Generate app key if not set
if ! grep -q "APP_KEY=base64:" .env; then
    print_status "Generating application key..."
    php artisan key:generate
fi

# Create queue tables
print_status "Creating queue tables..."
php artisan queue:table --quiet || true
php artisan queue:failed-table --quiet || true

# Run migrations
print_status "Running database migrations..."
php artisan migrate --force

# Step 4: Check OpenAI Configuration
print_step "4. Checking OpenAI Configuration..."

if ! grep -q "OPENAI_API_KEY=" .env || grep -q "OPENAI_API_KEY=$" .env; then
    print_warning "OpenAI API key is not configured in .env file"
    echo "Please add your OpenAI API key to the .env file:"
    echo "OPENAI_API_KEY=your_api_key_here"
    echo ""
    echo "Get your API key from: https://platform.openai.com/api-keys"
else
    print_status "OpenAI API key is configured ✓"
fi

# Step 5: Install Frontend Dependencies
print_step "5. Installing Frontend Dependencies..."

cd ../src

if [ -f "package.json" ]; then
    print_status "Installing Angular dependencies..."
    npm install
    print_status "Frontend dependencies installed ✓"
else
    print_warning "package.json not found in src directory"
fi

# Step 6: Build Frontend (optional)
print_step "6. Building Frontend..."

if [ -f "angular.json" ]; then
    print_status "Building Angular application..."
    npm run build --if-present
    print_status "Frontend build completed ✓"
fi

cd ..

# Step 7: Set Permissions (Linux/Mac only)
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    print_step "7. Setting Permissions..."
    
    cd laravel-backend
    
    # Set storage and cache permissions
    chmod -R 775 storage bootstrap/cache
    
    # Set ownership (if running as root)
    if [ "$EUID" -eq 0 ]; then
        chown -R www-data:www-data storage bootstrap/cache
    fi
    
    print_status "Permissions set ✓"
    
    cd ..
fi

# Step 8: Create Startup Scripts
print_step "8. Creating Startup Scripts..."

# Create development startup script
cat > start-dev.sh << 'EOF'
#!/bin/bash

echo "Starting development environment..."

# Start Laravel backend
cd laravel-backend
php artisan serve --host=0.0.0.0 --port=8000 &
LARAVEL_PID=$!

# Start queue worker
php artisan queue:work &
QUEUE_PID=$!

# Start Angular frontend
cd ../src
ng serve --host=0.0.0.0 --port=4200 &
ANGULAR_PID=$!

echo "Services started:"
echo "- Laravel Backend: http://localhost:8000"
echo "- Angular Frontend: http://localhost:4200"
echo "- Queue Worker: Running"

# Function to cleanup on exit
cleanup() {
    echo "Stopping services..."
    kill $LARAVEL_PID $QUEUE_PID $ANGULAR_PID 2>/dev/null
    exit
}

# Trap SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Wait for all background processes
wait
EOF

chmod +x start-dev.sh

print_status "Created start-dev.sh script ✓"

# Step 9: Verification
print_step "9. Running Verification Tests..."

cd laravel-backend

# Test database connection
print_status "Testing database connection..."
php artisan migrate:status > /dev/null 2>&1 && print_status "Database connection: OK ✓" || print_warning "Database connection: Failed"

# Test OpenAI service (if configured)
if grep -q "OPENAI_API_KEY=" .env && ! grep -q "OPENAI_API_KEY=$" .env; then
    print_status "Testing OpenAI service..."
    # This would require a custom artisan command to test OpenAI
    print_status "OpenAI service: Configured ✓"
fi

cd ..

# Final Summary
echo ""
echo "🎉 AI Integration Setup Complete!"
echo ""
echo "📋 Summary:"
echo "✅ Prerequisites checked"
echo "✅ Backend dependencies installed"
echo "✅ Database migrations completed"
echo "✅ Frontend dependencies installed"
echo "✅ Startup scripts created"
echo ""
echo "🚀 Next Steps:"
echo "1. Configure your OpenAI API key in laravel-backend/.env"
echo "2. Start the development environment: ./start-dev.sh"
echo "3. Visit http://localhost:4200 to access the application"
echo "4. Test the AI document analysis features"
echo ""
echo "📚 For detailed configuration, see: AI_INTEGRATION_SETUP.md"
echo ""
print_status "Setup completed successfully!"
