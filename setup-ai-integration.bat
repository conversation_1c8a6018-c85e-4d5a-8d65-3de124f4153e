@echo off
setlocal enabledelayedexpansion

REM AI Integration Setup Script for Windows
REM This script automates the setup process for AI document analysis and chatbot features

echo 🚀 Starting AI Integration Setup...

REM Check if we're in the right directory
if not exist "laravel-backend\artisan" (
    echo [ERROR] Please run this script from the project root directory
    pause
    exit /b 1
)

echo [STEP] 1. Checking Prerequisites...

REM Check PHP
php --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PHP is not installed or not in PATH
    pause
    exit /b 1
)
echo [INFO] PHP is installed ✓

REM Check Composer
composer --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Composer is not installed or not in PATH
    pause
    exit /b 1
)
echo [INFO] Composer is installed ✓

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    pause
    exit /b 1
)
echo [INFO] Node.js is installed ✓

echo [STEP] 2. Installing Backend Dependencies...

cd laravel-backend

REM Install PHP dependencies
echo [INFO] Installing PHP dependencies...
composer install --no-dev --optimize-autoloader

echo [STEP] 3. Setting up Database...

REM Check if .env exists
if not exist ".env" (
    echo [WARNING] .env file not found. Creating from .env.example...
    copy .env.example .env
    echo [INFO] Created .env file from .env.example
)

REM Generate app key if not set
findstr /C:"APP_KEY=base64:" .env >nul
if errorlevel 1 (
    echo [INFO] Generating application key...
    php artisan key:generate
)

REM Create queue tables
echo [INFO] Creating queue tables...
php artisan queue:table 2>nul
php artisan queue:failed-table 2>nul

REM Run migrations
echo [INFO] Running database migrations...
php artisan migrate --force

echo [STEP] 4. Checking OpenAI Configuration...

findstr /C:"OPENAI_API_KEY=" .env | findstr /V /C:"OPENAI_API_KEY=$" >nul
if errorlevel 1 (
    echo [WARNING] OpenAI API key is not configured in .env file
    echo Please add your OpenAI API key to the .env file:
    echo OPENAI_API_KEY=your_api_key_here
    echo.
    echo Get your API key from: https://platform.openai.com/api-keys
) else (
    echo [INFO] OpenAI API key is configured ✓
)

echo [STEP] 5. Installing Frontend Dependencies...

cd ..\src

if exist "package.json" (
    echo [INFO] Installing Angular dependencies...
    npm install
    echo [INFO] Frontend dependencies installed ✓
) else (
    echo [WARNING] package.json not found in src directory
)

echo [STEP] 6. Building Frontend...

if exist "angular.json" (
    echo [INFO] Building Angular application...
    npm run build 2>nul
    echo [INFO] Frontend build completed ✓
)

cd ..

echo [STEP] 7. Creating Startup Scripts...

REM Create development startup script
echo @echo off > start-dev.bat
echo echo Starting development environment... >> start-dev.bat
echo. >> start-dev.bat
echo REM Start Laravel backend >> start-dev.bat
echo start "Laravel Backend" cmd /k "cd laravel-backend && php artisan serve --host=0.0.0.0 --port=8000" >> start-dev.bat
echo. >> start-dev.bat
echo REM Start queue worker >> start-dev.bat
echo start "Queue Worker" cmd /k "cd laravel-backend && php artisan queue:work" >> start-dev.bat
echo. >> start-dev.bat
echo REM Start Angular frontend >> start-dev.bat
echo start "Angular Frontend" cmd /k "cd src && ng serve --host=0.0.0.0 --port=4200" >> start-dev.bat
echo. >> start-dev.bat
echo echo Services started: >> start-dev.bat
echo echo - Laravel Backend: http://localhost:8000 >> start-dev.bat
echo echo - Angular Frontend: http://localhost:4200 >> start-dev.bat
echo echo - Queue Worker: Running >> start-dev.bat
echo pause >> start-dev.bat

echo [INFO] Created start-dev.bat script ✓

echo [STEP] 8. Running Verification Tests...

cd laravel-backend

REM Test database connection
echo [INFO] Testing database connection...
php artisan migrate:status >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Database connection: Failed
) else (
    echo [INFO] Database connection: OK ✓
)

REM Test OpenAI service (if configured)
findstr /C:"OPENAI_API_KEY=" .env | findstr /V /C:"OPENAI_API_KEY=$" >nul
if not errorlevel 1 (
    echo [INFO] Testing OpenAI service...
    echo [INFO] OpenAI service: Configured ✓
)

cd ..

REM Final Summary
echo.
echo 🎉 AI Integration Setup Complete!
echo.
echo 📋 Summary:
echo ✅ Prerequisites checked
echo ✅ Backend dependencies installed
echo ✅ Database migrations completed
echo ✅ Frontend dependencies installed
echo ✅ Startup scripts created
echo.
echo 🚀 Next Steps:
echo 1. Configure your OpenAI API key in laravel-backend\.env
echo 2. Start the development environment: start-dev.bat
echo 3. Visit http://localhost:4200 to access the application
echo 4. Test the AI document analysis features
echo.
echo 📚 For detailed configuration, see: AI_INTEGRATION_SETUP.md
echo.
echo [INFO] Setup completed successfully!

pause
