<?php

return [

    /*
    |--------------------------------------------------------------------------
    | AI Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for AI-powered features
    | including document analysis, chatbot functionality, and OpenAI integration.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Analysis Settings
    |--------------------------------------------------------------------------
    */
    'analysis_async' => env('AI_ANALYSIS_ASYNC', true),
    'analysis_timeout' => env('AI_ANALYSIS_TIMEOUT', 300), // 5 minutes
    'analysis_retry_attempts' => env('AI_ANALYSIS_RETRY_ATTEMPTS', 3),

    /*
    |--------------------------------------------------------------------------
    | Document Processing
    |--------------------------------------------------------------------------
    */
    'max_document_size' => env('AI_MAX_DOCUMENT_SIZE', 10485760), // 10MB
    'supported_formats' => ['pdf', 'docx', 'doc'],
    'content_max_length' => env('AI_CONTENT_MAX_LENGTH', 50000), // characters

    /*
    |--------------------------------------------------------------------------
    | Chatbot Settings
    |--------------------------------------------------------------------------
    */
    'chat_max_message_length' => env('AI_CHAT_MAX_MESSAGE_LENGTH', 5000),
    'chat_conversation_history_limit' => env('AI_CHAT_HISTORY_LIMIT', 10),
    'chat_response_timeout' => env('AI_CHAT_RESPONSE_TIMEOUT', 60), // seconds

    /*
    |--------------------------------------------------------------------------
    | Legal Knowledge Base
    |--------------------------------------------------------------------------
    */
    'knowledge_base_enabled' => env('AI_KNOWLEDGE_BASE_ENABLED', true),
    'knowledge_base_cache_ttl' => env('AI_KNOWLEDGE_BASE_CACHE_TTL', 3600), // 1 hour

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limit_per_user_per_hour' => env('AI_RATE_LIMIT_PER_USER_PER_HOUR', 50),
    'rate_limit_per_user_per_day' => env('AI_RATE_LIMIT_PER_USER_PER_DAY', 200),

    /*
    |--------------------------------------------------------------------------
    | Caching
    |--------------------------------------------------------------------------
    */
    'cache_analysis_results' => env('AI_CACHE_ANALYSIS_RESULTS', true),
    'cache_ttl_analysis' => env('AI_CACHE_TTL_ANALYSIS', 86400), // 24 hours
    'cache_ttl_chat_context' => env('AI_CACHE_TTL_CHAT_CONTEXT', 3600), // 1 hour

    /*
    |--------------------------------------------------------------------------
    | Logging and Monitoring
    |--------------------------------------------------------------------------
    */
    'log_ai_interactions' => env('AI_LOG_INTERACTIONS', true),
    'log_performance_metrics' => env('AI_LOG_PERFORMANCE_METRICS', true),
    'monitor_token_usage' => env('AI_MONITOR_TOKEN_USAGE', true),

    /*
    |--------------------------------------------------------------------------
    | Security
    |--------------------------------------------------------------------------
    */
    'encrypt_stored_content' => env('AI_ENCRYPT_STORED_CONTENT', true),
    'anonymize_logs' => env('AI_ANONYMIZE_LOGS', true),
    'data_retention_days' => env('AI_DATA_RETENTION_DAYS', 365),

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        'document_analysis' => env('AI_FEATURE_DOCUMENT_ANALYSIS', true),
        'chatbot' => env('AI_FEATURE_CHATBOT', true),
        'recommendations' => env('AI_FEATURE_RECOMMENDATIONS', true),
        'legal_issue_detection' => env('AI_FEATURE_LEGAL_ISSUE_DETECTION', true),
        'batch_processing' => env('AI_FEATURE_BATCH_PROCESSING', false),
        'advanced_analytics' => env('AI_FEATURE_ADVANCED_ANALYTICS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Legal Issue Detection
    |--------------------------------------------------------------------------
    */
    'issue_detection' => [
        'confidence_threshold' => env('AI_ISSUE_CONFIDENCE_THRESHOLD', 70.0),
        'severity_mapping' => [
            'critical' => ['missing_termination_clause', 'unlimited_liability', 'no_dispute_resolution'],
            'high' => ['ambiguous_language', 'unfavorable_terms', 'compliance_issues'],
            'medium' => ['missing_definitions', 'unclear_obligations', 'inconsistent_terms'],
            'low' => ['formatting_issues', 'minor_language_improvements']
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Recommendation Engine
    |--------------------------------------------------------------------------
    */
    'recommendations' => [
        'max_per_analysis' => env('AI_MAX_RECOMMENDATIONS_PER_ANALYSIS', 20),
        'priority_threshold' => env('AI_RECOMMENDATION_PRIORITY_THRESHOLD', 60.0),
        'auto_accept_threshold' => env('AI_AUTO_ACCEPT_THRESHOLD', 90.0),
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Settings
    |--------------------------------------------------------------------------
    */
    'fallback' => [
        'enabled' => env('AI_FALLBACK_ENABLED', true),
        'use_rule_based_analysis' => env('AI_USE_RULE_BASED_FALLBACK', true),
        'basic_issue_detection' => env('AI_BASIC_ISSUE_DETECTION', true),
    ],

];
