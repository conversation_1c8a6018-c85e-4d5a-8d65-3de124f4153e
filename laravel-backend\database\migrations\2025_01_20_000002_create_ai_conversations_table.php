<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // AI Conversations Table
        Schema::create('ai_conversations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('document_analysis_id')->nullable();
            $table->string('conversation_title')->nullable();
            $table->enum('status', ['active', 'archived', 'deleted'])->default('active');
            $table->json('context_data')->nullable(); // Document context, user preferences, etc.
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['last_activity_at']);
        });

        // Chat Messages Table
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('conversation_id');
            $table->enum('sender_type', ['user', 'ai']); // Who sent the message
            $table->longText('message_content');
            $table->json('message_metadata')->nullable(); // Formatting, attachments, etc.
            $table->enum('message_type', ['text', 'analysis_result', 'recommendation', 'question', 'clarification'])->default('text');
            $table->json('referenced_entities')->nullable(); // Contract sections, legal issues, etc.
            $table->boolean('is_helpful')->nullable(); // User feedback on AI responses
            $table->text('user_feedback')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            $table->index(['conversation_id', 'created_at']);
            $table->index(['sender_type', 'message_type']);
        });

        // AI Processing Queue Table
        Schema::create('ai_processing_queue', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('job_type'); // 'document_analysis', 'chat_response', 'recommendation_generation'
            $table->json('job_data'); // Input data for the AI processing
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->text('error_message')->nullable();
            $table->json('result_data')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->index(['status', 'job_type']);
            $table->index(['user_id', 'status']);
        });

        // Legal Knowledge Base Table
        Schema::create('legal_knowledge_base', function (Blueprint $table) {
            $table->id();
            $table->string('category'); // 'clause_template', 'legal_concept', 'best_practice', 'regulation'
            $table->string('subcategory')->nullable();
            $table->string('title');
            $table->text('description');
            $table->longText('content');
            $table->json('tags')->nullable(); // For categorization and search
            $table->string('jurisdiction')->nullable(); // Legal jurisdiction if applicable
            $table->string('industry')->nullable(); // Industry-specific knowledge
            $table->enum('confidence_level', ['low', 'medium', 'high', 'expert_verified'])->default('medium');
            $table->text('source_reference')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index(['jurisdiction', 'industry']);
            $table->fullText(['title', 'description', 'content']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_knowledge_base');
        Schema::dropIfExists('ai_processing_queue');
        Schema::dropIfExists('chat_messages');
        Schema::dropIfExists('ai_conversations');
    }
};
