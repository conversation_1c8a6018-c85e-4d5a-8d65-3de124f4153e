<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DocumentAnalysis extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'contract_id',
        'unsigned_contract_id',
        'signed_contract_id',
        'document_type',
        'analysis_status',
        'content_extracted',
        'document_structure',
        'legal_entities',
        'total_issues_found',
        'total_recommendations',
        'confidence_score',
        'processing_time_seconds',
        'analysis_metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'document_structure' => 'array',
        'legal_entities' => 'array',
        'analysis_metadata' => 'array',
        'confidence_score' => 'decimal:2',
        'total_issues_found' => 'integer',
        'total_recommendations' => 'integer',
        'processing_time_seconds' => 'integer',
    ];

    /**
     * Get the user that owns the document analysis.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the contract associated with this analysis.
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    /**
     * Get the unsigned contract associated with this analysis.
     */
    public function unsignedContract(): BelongsTo
    {
        return $this->belongsTo(UnsignedContract::class);
    }

    /**
     * Get the signed contract associated with this analysis.
     */
    public function signedContract(): BelongsTo
    {
        return $this->belongsTo(SignedContract::class);
    }

    /**
     * Get the legal issues for this analysis.
     */
    public function legalIssues(): HasMany
    {
        return $this->hasMany(LegalIssue::class);
    }

    /**
     * Get the AI recommendations for this analysis.
     */
    public function aiRecommendations(): HasMany
    {
        return $this->hasMany(AiRecommendation::class);
    }

    /**
     * Get the AI conversations related to this analysis.
     */
    public function aiConversations(): HasMany
    {
        return $this->hasMany(AiConversation::class);
    }

    /**
     * Scope to filter by analysis status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('analysis_status', $status);
    }

    /**
     * Scope to filter by document type.
     */
    public function scopeByDocumentType($query, $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Get the associated document based on document type.
     */
    public function getDocumentAttribute()
    {
        switch ($this->document_type) {
            case 'contract':
                return $this->contract;
            case 'unsigned_contract':
                return $this->unsignedContract;
            case 'signed_contract':
                return $this->signedContract;
            default:
                return null;
        }
    }

    /**
     * Check if analysis is completed.
     */
    public function isCompleted(): bool
    {
        return $this->analysis_status === 'completed';
    }

    /**
     * Check if analysis has failed.
     */
    public function hasFailed(): bool
    {
        return $this->analysis_status === 'failed';
    }

    /**
     * Check if analysis is in progress.
     */
    public function isProcessing(): bool
    {
        return in_array($this->analysis_status, ['pending', 'processing']);
    }

    /**
     * Get high severity issues count.
     */
    public function getHighSeverityIssuesCountAttribute(): int
    {
        return $this->legalIssues()->whereIn('severity', ['high', 'critical'])->count();
    }

    /**
     * Get pending recommendations count.
     */
    public function getPendingRecommendationsCountAttribute(): int
    {
        return $this->aiRecommendations()->where('status', 'pending')->count();
    }
}
