<?php

namespace App\Services;

use App\Models\DocumentAnalysis;
use App\Models\LegalIssue;
use App\Models\AiRecommendation;
use App\Models\Contract;
use App\Models\UnsignedContract;
use App\Models\SignedContract;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

class AiDocumentAnalysisService
{
    protected $openAiService;
    protected $documentParsingService;

    public function __construct(
        OpenAiService $openAiService,
        DocumentParsingService $documentParsingService
    ) {
        $this->openAiService = $openAiService;
        $this->documentParsingService = $documentParsingService;
    }

    /**
     * Analyze a document and create analysis record
     */
    public function analyzeDocument($documentId, $documentType, $userId): DocumentAnalysis
    {
        Log::info("Starting document analysis", [
            'document_id' => $documentId,
            'document_type' => $documentType,
            'user_id' => $userId
        ]);

        // Create initial analysis record
        $analysis = $this->createAnalysisRecord($documentId, $documentType, $userId);

        try {
            // Update status to processing
            $analysis->update(['analysis_status' => 'processing']);

            // Get document and extract content
            $document = $this->getDocument($documentId, $documentType);
            $extractedContent = $this->extractDocumentContent($document);

            // Update analysis with extracted content
            $analysis->update([
                'content_extracted' => $extractedContent['text'],
                'document_structure' => $extractedContent['structure'],
                'legal_entities' => $extractedContent['entities']
            ]);

            // Perform AI analysis
            $aiAnalysisResult = $this->performAiAnalysis($extractedContent);

            // Process and store legal issues
            $issuesCount = $this->storeLegalIssues($analysis, $aiAnalysisResult['issues']);

            // Process and store recommendations
            $recommendationsCount = $this->storeRecommendations($analysis, $aiAnalysisResult['recommendations']);

            // Update analysis with final results
            $analysis->update([
                'analysis_status' => 'completed',
                'total_issues_found' => $issuesCount,
                'total_recommendations' => $recommendationsCount,
                'confidence_score' => $aiAnalysisResult['confidence_score'],
                'processing_time_seconds' => now()->diffInSeconds($analysis->created_at),
                'analysis_metadata' => $aiAnalysisResult['metadata']
            ]);

            Log::info("Document analysis completed successfully", [
                'analysis_id' => $analysis->id,
                'issues_found' => $issuesCount,
                'recommendations' => $recommendationsCount
            ]);

        } catch (Exception $e) {
            Log::error("Document analysis failed", [
                'analysis_id' => $analysis->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $analysis->update(['analysis_status' => 'failed']);
            throw $e;
        }

        return $analysis;
    }

    /**
     * Create initial analysis record
     */
    protected function createAnalysisRecord($documentId, $documentType, $userId): DocumentAnalysis
    {
        $data = [
            'user_id' => $userId,
            'document_type' => $documentType,
            'analysis_status' => 'pending'
        ];

        // Set the appropriate foreign key based on document type
        switch ($documentType) {
            case 'contract':
                $data['contract_id'] = $documentId;
                break;
            case 'unsigned_contract':
                $data['unsigned_contract_id'] = $documentId;
                break;
            case 'signed_contract':
                $data['signed_contract_id'] = $documentId;
                break;
        }

        return DocumentAnalysis::create($data);
    }

    /**
     * Get document model based on type
     */
    protected function getDocument($documentId, $documentType)
    {
        switch ($documentType) {
            case 'contract':
                return Contract::findOrFail($documentId);
            case 'unsigned_contract':
                return UnsignedContract::findOrFail($documentId);
            case 'signed_contract':
                return SignedContract::findOrFail($documentId);
            default:
                throw new Exception("Invalid document type: {$documentType}");
        }
    }

    /**
     * Extract content from document
     */
    protected function extractDocumentContent($document): array
    {
        $filePath = $document->file_path;
        
        if (!Storage::disk('public')->exists($filePath)) {
            throw new Exception("Document file not found: {$filePath}");
        }

        $fullPath = Storage::disk('public')->path($filePath);
        
        return $this->documentParsingService->parseDocument($fullPath);
    }

    /**
     * Perform AI analysis on extracted content
     */
    protected function performAiAnalysis(array $extractedContent): array
    {
        $prompt = $this->buildAnalysisPrompt($extractedContent);
        
        $aiResponse = $this->openAiService->analyzeDocument($prompt);
        
        return $this->parseAiResponse($aiResponse);
    }

    /**
     * Build prompt for AI analysis
     */
    protected function buildAnalysisPrompt(array $extractedContent): string
    {
        $text = $extractedContent['text'];
        $structure = json_encode($extractedContent['structure']);
        
        return "
        Please analyze the following legal document for potential issues and provide recommendations.
        
        Document Text:
        {$text}
        
        Document Structure:
        {$structure}
        
        Please identify:
        1. Missing essential clauses (termination, liability, dispute resolution, etc.)
        2. Ambiguous or unclear language
        3. Unfavorable terms
        4. Compliance issues
        5. Inconsistent or contradictory provisions
        
        For each issue found, provide:
        - Issue type and severity (low, medium, high, critical)
        - Description and location
        - Suggested fix
        - Legal reasoning
        
        Also provide specific recommendations for improvement with implementation guidance.
        
        Return the response in JSON format with 'issues' and 'recommendations' arrays.
        ";
    }

    /**
     * Parse AI response into structured data
     */
    protected function parseAiResponse(string $aiResponse): array
    {
        try {
            $decoded = json_decode($aiResponse, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Invalid JSON response from AI service");
            }
            
            return [
                'issues' => $decoded['issues'] ?? [],
                'recommendations' => $decoded['recommendations'] ?? [],
                'confidence_score' => $decoded['confidence_score'] ?? 75.0,
                'metadata' => $decoded['metadata'] ?? []
            ];
            
        } catch (Exception $e) {
            Log::warning("Failed to parse AI response, using fallback", [
                'error' => $e->getMessage(),
                'response' => substr($aiResponse, 0, 500)
            ]);
            
            return $this->createFallbackAnalysis($aiResponse);
        }
    }

    /**
     * Create fallback analysis when AI response parsing fails
     */
    protected function createFallbackAnalysis(string $aiResponse): array
    {
        return [
            'issues' => [
                [
                    'issue_type' => 'analysis_incomplete',
                    'severity' => 'medium',
                    'category' => 'structural',
                    'title' => 'Analysis Incomplete',
                    'description' => 'The automated analysis could not be completed fully. Manual review recommended.',
                    'suggested_fix' => 'Please review the document manually or try the analysis again.',
                    'legal_reasoning' => 'Technical limitation in automated analysis.',
                    'confidence_score' => 50.0
                ]
            ],
            'recommendations' => [
                [
                    'recommendation_type' => 'manual_review',
                    'priority' => 'medium',
                    'title' => 'Manual Review Recommended',
                    'description' => 'Consider having this document reviewed by a legal professional.',
                    'implementation_guide' => 'Contact a qualified attorney for comprehensive document review.',
                    'legal_rationale' => 'Automated analysis limitations require human expertise.',
                    'impact_score' => 70.0
                ]
            ],
            'confidence_score' => 50.0,
            'metadata' => [
                'analysis_type' => 'fallback',
                'ai_response_preview' => substr($aiResponse, 0, 200)
            ]
        ];
    }

    /**
     * Store legal issues from analysis
     */
    protected function storeLegalIssues(DocumentAnalysis $analysis, array $issues): int
    {
        $count = 0;
        
        foreach ($issues as $issueData) {
            LegalIssue::create([
                'document_analysis_id' => $analysis->id,
                'issue_type' => $issueData['issue_type'] ?? 'unknown',
                'severity' => $issueData['severity'] ?? 'medium',
                'category' => $issueData['category'] ?? 'content',
                'title' => $issueData['title'] ?? 'Legal Issue Detected',
                'description' => $issueData['description'] ?? '',
                'location_in_document' => $issueData['location'] ?? null,
                'suggested_fix' => $issueData['suggested_fix'] ?? null,
                'legal_reasoning' => $issueData['legal_reasoning'] ?? null,
                'confidence_score' => $issueData['confidence_score'] ?? 75.0
            ]);
            
            $count++;
        }
        
        return $count;
    }

    /**
     * Store recommendations from analysis
     */
    protected function storeRecommendations(DocumentAnalysis $analysis, array $recommendations): int
    {
        $count = 0;
        
        foreach ($recommendations as $recData) {
            AiRecommendation::create([
                'document_analysis_id' => $analysis->id,
                'recommendation_type' => $recData['recommendation_type'] ?? 'general',
                'priority' => $recData['priority'] ?? 'medium',
                'title' => $recData['title'] ?? 'Improvement Recommendation',
                'description' => $recData['description'] ?? '',
                'implementation_guide' => $recData['implementation_guide'] ?? null,
                'original_text' => $recData['original_text'] ?? null,
                'suggested_text' => $recData['suggested_text'] ?? null,
                'location_references' => $recData['location_references'] ?? null,
                'legal_rationale' => $recData['legal_rationale'] ?? null,
                'impact_score' => $recData['impact_score'] ?? 70.0
            ]);
            
            $count++;
        }
        
        return $count;
    }

    /**
     * Get analysis summary for a document
     */
    public function getAnalysisSummary($documentId, $documentType): ?array
    {
        $analysis = DocumentAnalysis::where('document_type', $documentType)
            ->where($documentType . '_id', $documentId)
            ->where('analysis_status', 'completed')
            ->latest()
            ->first();

        if (!$analysis) {
            return null;
        }

        return [
            'analysis_id' => $analysis->id,
            'status' => $analysis->analysis_status,
            'total_issues' => $analysis->total_issues_found,
            'total_recommendations' => $analysis->total_recommendations,
            'confidence_score' => $analysis->confidence_score,
            'high_severity_issues' => $analysis->legalIssues()->whereIn('severity', ['high', 'critical'])->count(),
            'pending_recommendations' => $analysis->aiRecommendations()->where('status', 'pending')->count(),
            'created_at' => $analysis->created_at,
            'processing_time' => $analysis->processing_time_seconds
        ];
    }
}
