<?php

/**
 * AI Integration Test Script
 * 
 * This script tests the AI document analysis and chatbot integration
 * Run this from the laravel-backend directory: php ../test-ai-integration.php
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Colors for console output
class Console {
    const RED = "\033[31m";
    const GREEN = "\033[32m";
    const YELLOW = "\033[33m";
    const BLUE = "\033[34m";
    const RESET = "\033[0m";
    
    public static function success($message) {
        echo self::GREEN . "[✓] " . $message . self::RESET . "\n";
    }
    
    public static function error($message) {
        echo self::RED . "[✗] " . $message . self::RESET . "\n";
    }
    
    public static function warning($message) {
        echo self::YELLOW . "[!] " . $message . self::RESET . "\n";
    }
    
    public static function info($message) {
        echo self::BLUE . "[i] " . $message . self::RESET . "\n";
    }
    
    public static function step($message) {
        echo self::BLUE . "\n=== " . $message . " ===" . self::RESET . "\n";
    }
}

// Check if we're in the right directory
if (!file_exists('laravel-backend/artisan')) {
    Console::error("Please run this script from the project root directory");
    exit(1);
}

Console::info("🧪 AI Integration Test Suite");
Console::info("Testing AI document analysis and chatbot features...\n");

// Change to Laravel directory
chdir('laravel-backend');

// Bootstrap Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

Console::step("1. Environment Configuration Tests");

// Test 1: Check OpenAI API Key
$openaiKey = config('services.openai.api_key');
if (empty($openaiKey)) {
    Console::error("OpenAI API key is not configured");
} else {
    Console::success("OpenAI API key is configured");
}

// Test 2: Check AI Configuration
$aiConfig = [
    'AI_EXTRACTION_ENABLED' => env('AI_EXTRACTION_ENABLED'),
    'AI_ANALYSIS_ENABLED' => env('AI_ANALYSIS_ENABLED'),
    'AI_CHATBOT_ENABLED' => env('AI_CHATBOT_ENABLED'),
    'AI_ANALYSIS_ASYNC' => env('AI_ANALYSIS_ASYNC'),
];

foreach ($aiConfig as $key => $value) {
    if ($value === null) {
        Console::warning("$key is not configured");
    } else {
        Console::success("$key = " . ($value ? 'true' : 'false'));
    }
}

Console::step("2. Database Tests");

// Test 3: Check Database Connection
try {
    DB::connection()->getPdo();
    Console::success("Database connection successful");
} catch (Exception $e) {
    Console::error("Database connection failed: " . $e->getMessage());
}

// Test 4: Check Required Tables
$requiredTables = [
    'document_analyses',
    'legal_issues',
    'ai_recommendations',
    'ai_conversations',
    'chat_messages',
    'jobs',
    'failed_jobs'
];

foreach ($requiredTables as $table) {
    try {
        if (Schema::hasTable($table)) {
            Console::success("Table '$table' exists");
        } else {
            Console::error("Table '$table' does not exist");
        }
    } catch (Exception $e) {
        Console::error("Error checking table '$table': " . $e->getMessage());
    }
}

Console::step("3. Service Tests");

// Test 5: OpenAI Service
try {
    $openaiService = app(\App\Services\OpenAiService::class);
    $status = $openaiService->getServiceStatus();
    
    if ($status['api_configured']) {
        Console::success("OpenAI service is configured");
        Console::info("Model: " . $status['model']);
        Console::info("Max tokens: " . $status['max_tokens']);
        
        if ($status['available']) {
            Console::success("OpenAI API is accessible");
        } else {
            Console::warning("OpenAI API is not accessible (check network/API key)");
        }
    } else {
        Console::error("OpenAI service is not configured");
    }
} catch (Exception $e) {
    Console::error("OpenAI service test failed: " . $e->getMessage());
}

// Test 6: AI Document Analysis Service
try {
    $analysisService = app(\App\Services\AiDocumentAnalysisService::class);
    Console::success("AI Document Analysis service is available");
} catch (Exception $e) {
    Console::error("AI Document Analysis service failed: " . $e->getMessage());
}

// Test 7: AI Chatbot Service
try {
    $chatbotService = app(\App\Services\AiChatbotService::class);
    Console::success("AI Chatbot service is available");
} catch (Exception $e) {
    Console::error("AI Chatbot service failed: " . $e->getMessage());
}

Console::step("4. Queue Tests");

// Test 8: Queue Configuration
$queueConnection = config('queue.default');
Console::info("Queue connection: $queueConnection");

if ($queueConnection === 'database') {
    try {
        // Check if jobs table exists and is accessible
        $jobsCount = DB::table('jobs')->count();
        Console::success("Queue database connection working (jobs: $jobsCount)");
    } catch (Exception $e) {
        Console::error("Queue database connection failed: " . $e->getMessage());
    }
}

Console::step("5. API Endpoint Tests");

// Test 9: Test API Routes
$routes = [
    '/api/ai/analysis/dashboard-summary',
    '/api/ai/chat/conversations',
];

foreach ($routes as $route) {
    try {
        // Create a mock request
        $request = Request::create($route, 'GET');
        $request->headers->set('Accept', 'application/json');
        
        // This is a basic route existence check
        Console::info("Route '$route' is defined");
    } catch (Exception $e) {
        Console::warning("Route '$route' test failed: " . $e->getMessage());
    }
}

Console::step("6. File Permissions Tests");

// Test 10: Check Storage Permissions
$storageDirectories = [
    'storage/app',
    'storage/logs',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views',
];

foreach ($storageDirectories as $dir) {
    if (is_writable($dir)) {
        Console::success("Directory '$dir' is writable");
    } else {
        Console::error("Directory '$dir' is not writable");
    }
}

Console::step("7. Configuration Summary");

// Test 11: Configuration Summary
$summary = [
    'PHP Version' => PHP_VERSION,
    'Laravel Version' => app()->version(),
    'Environment' => app()->environment(),
    'Debug Mode' => config('app.debug') ? 'Enabled' : 'Disabled',
    'Queue Driver' => config('queue.default'),
    'OpenAI Model' => config('services.openai.model'),
    'AI Features' => env('AI_ANALYSIS_ENABLED') ? 'Enabled' : 'Disabled',
];

foreach ($summary as $key => $value) {
    Console::info("$key: $value");
}

Console::step("Test Results Summary");

Console::info("🧪 AI Integration Test Suite Completed");
Console::info("Review the results above to ensure all components are working correctly.");
Console::info("");
Console::info("Next steps:");
Console::info("1. If any tests failed, check the configuration and fix the issues");
Console::info("2. Start the queue worker: php artisan queue:work");
Console::info("3. Test the frontend integration at http://localhost:4200");
Console::info("4. Upload a document and test the AI analysis features");
Console::info("");
Console::success("Test suite completed successfully!");

?>
