import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AiChatbotService, DocumentAnalysis, LegalIssue, AiRecommendation } from '../../services/ai-chatbot.service';

@Component({
  selector: 'app-document-analysis',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="document-analysis-container">
      <!-- Analysis Summary Card -->
      <div class="analysis-summary-card" *ngIf="analysisSummary">
        <div class="card-header">
          <h4><i class="fas fa-chart-line"></i> Document Analysis</h4>
          <span class="status-badge" [class]="'status-' + analysisSummary.status">
            {{ analysisSummary.status | titlecase }}
          </span>
        </div>
        
        <div class="summary-stats">
          <div class="stat-item">
            <div class="stat-number">{{ analysisSummary.total_issues }}</div>
            <div class="stat-label">Issues Found</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ analysisSummary.total_recommendations }}</div>
            <div class="stat-label">Recommendations</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ analysisSummary.confidence_score }}%</div>
            <div class="stat-label">Confidence</div>
          </div>
        </div>

        <div class="analysis-actions">
          <button 
            class="btn btn-primary btn-sm" 
            (click)="startAnalysis()" 
            [disabled]="isAnalyzing"
            *ngIf="!analysisSummary || analysisSummary.status === 'failed'"
          >
            <i class="fas" [class.fa-sync]="!isAnalyzing" [class.fa-spinner]="isAnalyzing" [class.fa-spin]="isAnalyzing"></i>
            {{ isAnalyzing ? 'Analyzing...' : 'Start Analysis' }}
          </button>
          
          <button 
            class="btn btn-outline-primary btn-sm" 
            (click)="openChatbot()"
            *ngIf="analysisSummary && analysisSummary.status === 'completed'"
          >
            <i class="fas fa-comments"></i>
            Ask AI Assistant
          </button>
          
          <button 
            class="btn btn-outline-secondary btn-sm" 
            (click)="viewDetails()"
            *ngIf="analysisSummary && analysisSummary.status === 'completed'"
          >
            <i class="fas fa-eye"></i>
            View Details
          </button>
        </div>
      </div>

      <!-- No Analysis State -->
      <div class="no-analysis-state" *ngIf="!analysisSummary && !isAnalyzing">
        <div class="empty-state-content">
          <i class="fas fa-robot fa-2x"></i>
          <h4>AI Document Analysis</h4>
          <p>Get AI-powered insights about your contract including legal issue detection and improvement recommendations.</p>
          <button class="btn btn-primary" (click)="startAnalysis()">
            <i class="fas fa-play"></i>
            Start AI Analysis
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div class="loading-state" *ngIf="isAnalyzing">
        <div class="loading-content">
          <div class="spinner">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
          </div>
          <h4>Analyzing Document...</h4>
          <p>Our AI is reviewing your contract for legal issues and improvement opportunities.</p>
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="analysisProgress"></div>
          </div>
        </div>
      </div>

      <!-- Quick Issues Preview -->
      <div class="quick-issues-preview" *ngIf="highPriorityIssues.length > 0">
        <h5><i class="fas fa-exclamation-triangle"></i> High Priority Issues</h5>
        <div class="issue-item" *ngFor="let issue of highPriorityIssues.slice(0, 3)">
          <div class="issue-severity" [class]="'severity-' + issue.severity">
            <i class="fas" [class]="issue.severity_icon"></i>
          </div>
          <div class="issue-content">
            <div class="issue-title">{{ issue.title }}</div>
            <div class="issue-description">{{ issue.description | slice:0:100 }}...</div>
          </div>
        </div>
        <button class="btn btn-link btn-sm" (click)="viewDetails()" *ngIf="highPriorityIssues.length > 3">
          View {{ highPriorityIssues.length - 3 }} more issues
        </button>
      </div>
    </div>
  `,
  styles: [`
    .document-analysis-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .analysis-summary-card {
      padding: 20px;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .card-header h4 {
      margin: 0;
      color: #333;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 0.8em;
      font-weight: 500;
    }

    .status-completed { background: #e8f5e8; color: #2e7d32; }
    .status-processing { background: #fff3e0; color: #f57c00; }
    .status-pending { background: #e3f2fd; color: #1976d2; }
    .status-failed { background: #ffebee; color: #d32f2f; }

    .summary-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      margin-bottom: 20px;
    }

    .stat-item {
      text-align: center;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .stat-number {
      font-size: 1.8em;
      font-weight: bold;
      color: #667eea;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 0.9em;
      color: #666;
    }

    .analysis-actions {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .no-analysis-state, .loading-state {
      padding: 40px 20px;
      text-align: center;
    }

    .empty-state-content i {
      color: #667eea;
      margin-bottom: 15px;
    }

    .empty-state-content h4 {
      color: #333;
      margin-bottom: 10px;
    }

    .empty-state-content p {
      color: #666;
      margin-bottom: 20px;
      line-height: 1.5;
    }

    .loading-content .spinner {
      color: #667eea;
      margin-bottom: 20px;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #e0e0e0;
      border-radius: 2px;
      overflow: hidden;
      margin-top: 15px;
    }

    .progress-fill {
      height: 100%;
      background: #667eea;
      transition: width 0.3s ease;
    }

    .quick-issues-preview {
      border-top: 1px solid #e0e0e0;
      padding: 20px;
      background: #fafafa;
    }

    .quick-issues-preview h5 {
      margin: 0 0 15px 0;
      color: #f57c00;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .issue-item {
      display: flex;
      gap: 12px;
      padding: 12px;
      background: white;
      border-radius: 6px;
      margin-bottom: 10px;
      border-left: 3px solid #f57c00;
    }

    .issue-severity {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .severity-critical { background: #ffebee; color: #d32f2f; }
    .severity-high { background: #fff3e0; color: #f57c00; }
    .severity-medium { background: #fff8e1; color: #fbc02d; }
    .severity-low { background: #e8f5e8; color: #388e3c; }

    .issue-content {
      flex: 1;
    }

    .issue-title {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .issue-description {
      font-size: 0.9em;
      color: #666;
      line-height: 1.4;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 0.9em;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      text-decoration: none;
    }

    .btn-primary {
      background-color: #667eea;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background-color: #5a6fd8;
    }

    .btn-outline-primary {
      background-color: transparent;
      color: #667eea;
      border: 1px solid #667eea;
    }

    .btn-outline-primary:hover:not(:disabled) {
      background-color: #667eea;
      color: white;
    }

    .btn-outline-secondary {
      background-color: transparent;
      color: #6c757d;
      border: 1px solid #6c757d;
    }

    .btn-outline-secondary:hover:not(:disabled) {
      background-color: #6c757d;
      color: white;
    }

    .btn-link {
      background: none;
      color: #667eea;
      border: none;
      text-decoration: underline;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 0.8em;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    @media (max-width: 768px) {
      .summary-stats {
        grid-template-columns: 1fr;
        gap: 10px;
      }
      
      .analysis-actions {
        flex-direction: column;
      }
      
      .btn {
        width: 100%;
        justify-content: center;
      }
    }
  `]
})
export class DocumentAnalysisComponent implements OnInit {
  @Input() documentId!: number;
  @Input() documentType!: string;

  analysisSummary: any = null;
  highPriorityIssues: LegalIssue[] = [];
  isAnalyzing: boolean = false;
  analysisProgress: number = 0;

  constructor(private aiChatbotService: AiChatbotService) {}

  ngOnInit(): void {
    this.loadAnalysisSummary();
  }

  loadAnalysisSummary(): void {
    if (!this.documentId || !this.documentType) return;

    this.aiChatbotService.getDocumentAnalysisSummary(this.documentId, this.documentType)
      .subscribe({
        next: (summary) => {
          this.analysisSummary = summary;
          if (summary && summary.analysis_id) {
            this.loadHighPriorityIssues(summary.analysis_id);
          }
        },
        error: (error) => {
          console.error('Failed to load analysis summary:', error);
        }
      });
  }

  loadHighPriorityIssues(analysisId: number): void {
    this.aiChatbotService.getLegalIssues(analysisId).subscribe({
      next: (data) => {
        this.highPriorityIssues = data.issues.filter(issue => 
          issue.severity === 'high' || issue.severity === 'critical'
        );
      },
      error: (error) => {
        console.error('Failed to load legal issues:', error);
      }
    });
  }

  startAnalysis(): void {
    if (!this.documentId || !this.documentType) return;

    this.isAnalyzing = true;
    this.analysisProgress = 0;

    // Simulate progress
    const progressInterval = setInterval(() => {
      this.analysisProgress += Math.random() * 15;
      if (this.analysisProgress >= 90) {
        this.analysisProgress = 90;
        clearInterval(progressInterval);
      }
    }, 1000);

    this.aiChatbotService.analyzeDocument(this.documentId, this.documentType)
      .subscribe({
        next: (result) => {
          clearInterval(progressInterval);
          this.analysisProgress = 100;
          
          setTimeout(() => {
            this.isAnalyzing = false;
            this.loadAnalysisSummary();
          }, 1000);
        },
        error: (error) => {
          clearInterval(progressInterval);
          this.isAnalyzing = false;
          this.analysisProgress = 0;
          console.error('Analysis failed:', error);
        }
      });
  }

  openChatbot(): void {
    // This would typically emit an event to the parent component
    // to open the AI chatbot with the document context
    console.log('Opening AI chatbot for document analysis');
  }

  viewDetails(): void {
    // This would typically navigate to a detailed analysis view
    // or open a modal with full analysis results
    console.log('Viewing detailed analysis results');
  }
}
