# AI Document Analysis & Chatbot Integration Setup Guide

This guide provides complete setup instructions for integrating AI-powered document analysis and chatbot features into your contract management system.

## 📋 Prerequisites

- Laravel 12.x
- Angular 18+
- PHP 8.2+
- MySQL/PostgreSQL database
- Node.js 18+
- Composer
- OpenAI API account

## 🔧 1. Environment Configuration

### Backend (.env file)

Add the following configuration to your `laravel-backend/.env` file:

```env
# AI Document Analysis & Chatbot Configuration
# Get an API key from https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
OPENAI_BASE_URL=https://api.openai.com/v1

# AI Features Configuration
AI_EXTRACTION_ENABLED=true
AI_ANALYSIS_ENABLED=true
AI_CHATBOT_ENABLED=true
AI_ANALYSIS_ASYNC=true

# AI Processing Limits
AI_MAX_DOCUMENT_SIZE=********
AI_CONTENT_MAX_LENGTH=50000
AI_CHAT_MAX_MESSAGE_LENGTH=5000
AI_CHAT_HISTORY_LIMIT=10
AI_CHAT_RESPONSE_TIMEOUT=60

# Queue Configuration for AI Processing
QUEUE_CONNECTION=database
```

### Frontend Environment

Ensure your Angular environment files include the API URL:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8000/api'
};
```

## 🚀 2. Dependencies Installation

### Backend Dependencies

The required dependencies are already included in composer.json:

```bash
cd laravel-backend

# Install PHP dependencies
composer install

# Install additional dependencies if needed
composer require guzzlehttp/guzzle
```

### Frontend Dependencies

```bash
cd src

# Install Angular dependencies
npm install

# Install additional UI dependencies if needed
npm install @angular/animations
npm install chart.js
```

## 🗄️ 3. Database Setup

### Run Migrations

```bash
cd laravel-backend

# Create queue tables
php artisan queue:table
php artisan queue:failed-table

# Run all migrations
php artisan migrate

# Seed admin user (optional)
php artisan db:seed --class=AdminUserSeeder
```

### Database Tables Created

The following tables will be created for AI functionality:
- `document_analyses` - Stores document analysis results
- `legal_issues` - Stores identified legal issues
- `ai_recommendations` - Stores AI recommendations
- `ai_conversations` - Stores chatbot conversations
- `chat_messages` - Stores chat messages
- `jobs` - Queue jobs for async processing
- `failed_jobs` - Failed queue jobs

## ⚙️ 4. Service Provider Registration

The AI Service Provider is automatically registered in `bootstrap/providers.php`:

```php
return [
    App\Providers\AppServiceProvider::class,
    App\Providers\AuthServiceProvider::class,
    App\Providers\AiServiceProvider::class, // ✅ Added
    // ... other providers
];
```

## 🔄 5. Queue Configuration

### Start Queue Worker

For AI document analysis to work properly, start the queue worker:

```bash
cd laravel-backend

# Start queue worker (development)
php artisan queue:work

# Or use the dev script that includes queue worker
composer run dev
```

### Production Queue Setup

For production, use a process manager like Supervisor:

```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/project/artisan queue:work --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/worker.log
```

## 🔑 6. OpenAI API Key Setup

### Get OpenAI API Key

1. Sign up at [https://platform.openai.com/](https://platform.openai.com/)
2. Navigate to API Keys section
3. Create a new API key
4. Copy the key and add it to your `.env` file

### Test API Connection

```bash
cd laravel-backend

# Test OpenAI connection
php artisan tinker

# In tinker console:
$service = app(\App\Services\OpenAiService::class);
$status = $service->getServiceStatus();
dd($status);
```

## 🧪 7. Testing Integration

### Backend API Testing

Test the document analysis endpoints:

```bash
# Test analysis summary endpoint
curl -X GET "http://localhost:8000/api/ai/analysis/dashboard-summary" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# Test document analysis
curl -X POST "http://localhost:8000/api/ai/analysis/analyze" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"document_id": 1, "document_type": "unsigned_contract"}'
```

### Frontend Testing

1. Start the Angular development server:
```bash
cd src
ng serve
```

2. Navigate to the dashboard at `http://localhost:4200/dashboard`
3. Look for the "AI Document Analysis" widget
4. Upload a contract and test the analysis features

## 🎨 8. Customization

### Customize AI Prompts

Edit the prompts in `laravel-backend/app/Services/OpenAiService.php`:

```php
protected function getSystemPrompt(): string
{
    return "You are a legal document analysis expert. Analyze contracts for:
    1. Missing essential clauses
    2. Ambiguous language
    3. Unfavorable terms
    4. Compliance issues
    5. Risk factors
    
    Provide structured JSON responses with issues and recommendations.";
}
```

### Customize Dashboard Widget

The document analysis widget can be customized in:
- `src/app/components/dashboard/dashboard.component.html` - Template
- `src/app/components/dashboard/dashboard.component.css` - Styles
- `src/app/components/dashboard/dashboard.component.ts` - Logic

## 🔧 9. Integration Points

### Add to Existing Views

To integrate the document analysis component into other views:

```typescript
// Import the component
import { DocumentAnalysisComponent } from './components/document-analysis/document-analysis.component';

// Add to component imports
@Component({
  imports: [DocumentAnalysisComponent, ...],
  // ...
})

// Use in template
<app-document-analysis 
  [documentId]="contract.id" 
  [documentType]="'unsigned_contract'">
</app-document-analysis>
```

### Add Analysis Button to Contract Lists

```html
<button class="btn btn-primary" (click)="analyzeContract(contract)">
  <i class="fas fa-robot"></i>
  Analyze with AI
</button>
```

## 🚨 10. Troubleshooting

### Common Issues

1. **OpenAI API Key Invalid**
   - Verify the API key in `.env`
   - Check API key permissions and billing

2. **Queue Jobs Not Processing**
   - Ensure queue worker is running
   - Check `failed_jobs` table for errors

3. **Database Connection Issues**
   - Verify database credentials in `.env`
   - Run `php artisan migrate:status`

4. **CORS Issues**
   - Check CORS middleware configuration
   - Verify frontend API URL

### Debug Mode

Enable debug logging in `.env`:

```env
APP_DEBUG=true
LOG_LEVEL=debug
```

Check logs in `laravel-backend/storage/logs/laravel.log`

## 📚 11. Additional Resources

- [OpenAI API Documentation](https://platform.openai.com/docs)
- [Laravel Queue Documentation](https://laravel.com/docs/queues)
- [Angular HTTP Client Guide](https://angular.io/guide/http)

## ✅ 12. Verification Checklist

- [ ] OpenAI API key configured and tested
- [ ] Database migrations completed
- [ ] Queue worker running
- [ ] AI Service Provider registered
- [ ] Frontend components integrated
- [ ] Dashboard widget displaying
- [ ] Document analysis working
- [ ] Chatbot functionality active
- [ ] Error handling implemented
- [ ] Logging configured

## 🎯 Next Steps

1. Test document analysis with sample contracts
2. Customize AI prompts for your specific needs
3. Add more integration points throughout your application
4. Set up monitoring and alerting for AI services
5. Consider implementing caching for frequently analyzed documents
