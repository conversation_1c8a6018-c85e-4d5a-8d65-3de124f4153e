<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AiDocumentAnalysisService;
use App\Models\DocumentAnalysis;
use App\Models\LegalIssue;
use App\Models\AiRecommendation;
use App\Jobs\AnalyzeDocumentJob;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class DocumentAnalysisController extends Controller
{
    protected $analysisService;

    public function __construct(AiDocumentAnalysisService $analysisService)
    {
        $this->analysisService = $analysisService;
        $this->middleware('auth:sanctum');
    }

    /**
     * Start document analysis
     */
    public function analyzeDocument(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'document_id' => 'required|integer',
                'document_type' => 'required|string|in:contract,unsigned_contract,signed_contract'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $documentId = $request->input('document_id');
            $documentType = $request->input('document_type');

            // Verify user owns the document
            $documentModel = $this->getDocumentModel($documentType);
            $document = $documentModel::where('id', $documentId)
                ->where('user_id', $user->id)
                ->first();

            if (!$document) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document not found or access denied'
                ], 404);
            }

            // Check if analysis already exists and is recent
            $existingAnalysis = DocumentAnalysis::where('user_id', $user->id)
                ->where('document_type', $documentType)
                ->where($documentType . '_id', $documentId)
                ->where('created_at', '>', now()->subHours(24))
                ->latest()
                ->first();

            if ($existingAnalysis && $existingAnalysis->analysis_status === 'completed') {
                return response()->json([
                    'success' => true,
                    'message' => 'Analysis already exists',
                    'data' => [
                        'analysis' => $this->formatAnalysisResponse($existingAnalysis),
                        'is_existing' => true
                    ]
                ]);
            }

            // Start analysis (can be synchronous or asynchronous)
            if (config('ai.analysis_async', true)) {
                // Queue the analysis job
                AnalyzeDocumentJob::dispatch($documentId, $documentType, $user->id);

                return response()->json([
                    'success' => true,
                    'message' => 'Document analysis started',
                    'data' => [
                        'status' => 'processing',
                        'estimated_completion' => now()->addMinutes(5)->toISOString()
                    ]
                ]);
            } else {
                // Perform analysis synchronously
                $analysis = $this->analysisService->analyzeDocument($documentId, $documentType, $user->id);

                return response()->json([
                    'success' => true,
                    'message' => 'Document analysis completed',
                    'data' => [
                        'analysis' => $this->formatAnalysisResponse($analysis)
                    ]
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to start document analysis', [
                'user_id' => Auth::id(),
                'document_id' => $request->input('document_id'),
                'document_type' => $request->input('document_type'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to start document analysis'
            ], 500);
        }
    }

    /**
     * Get analysis status and results
     */
    public function getAnalysis(Request $request, int $analysisId): JsonResponse
    {
        try {
            $user = Auth::user();
            $analysis = DocumentAnalysis::where('id', $analysisId)
                ->where('user_id', $user->id)
                ->with(['legalIssues', 'aiRecommendations'])
                ->first();

            if (!$analysis) {
                return response()->json([
                    'success' => false,
                    'message' => 'Analysis not found or access denied'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'analysis' => $this->formatAnalysisResponse($analysis)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get document analysis', [
                'user_id' => Auth::id(),
                'analysis_id' => $analysisId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve analysis'
            ], 500);
        }
    }

    /**
     * Get analysis summary for a document
     */
    public function getDocumentAnalysisSummary(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'document_id' => 'required|integer',
                'document_type' => 'required|string|in:contract,unsigned_contract,signed_contract'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $documentId = $request->input('document_id');
            $documentType = $request->input('document_type');

            $summary = $this->analysisService->getAnalysisSummary($documentId, $documentType);

            if (!$summary) {
                return response()->json([
                    'success' => false,
                    'message' => 'No analysis found for this document'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => $summary
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get document analysis summary', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve analysis summary'
            ], 500);
        }
    }

    /**
     * Get dashboard analysis summary for the authenticated user
     */
    public function getDashboardAnalysisSummary(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            // Get all analyses for the user
            $analyses = DocumentAnalysis::where('user_id', $user->id)
                ->with(['legalIssues', 'aiRecommendations'])
                ->get();

            if ($analyses->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'data' => null
                ]);
            }

            // Calculate summary statistics
            $totalAnalyses = $analyses->count();
            $completedAnalyses = $analyses->where('analysis_status', 'completed')->count();
            $totalIssues = $analyses->sum('total_issues_found');
            $totalRecommendations = $analyses->sum('total_recommendations');

            // Get the most recent analysis
            $recentAnalysis = $analyses->sortByDesc('created_at')->first();

            $summary = [
                'total_analyses' => $totalAnalyses,
                'completed_analyses' => $completedAnalyses,
                'issues_found' => $totalIssues,
                'recommendations' => $totalRecommendations,
                'recent_analysis' => $recentAnalysis ? [
                    'id' => $recentAnalysis->id,
                    'document_type' => $recentAnalysis->document_type,
                    'status' => $recentAnalysis->analysis_status,
                    'issues_count' => $recentAnalysis->total_issues_found,
                    'recommendations_count' => $recentAnalysis->total_recommendations,
                    'created_at' => $recentAnalysis->created_at
                ] : null
            ];

            return response()->json([
                'success' => true,
                'data' => $summary
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get dashboard analysis summary', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve dashboard analysis summary'
            ], 500);
        }
    }

    /**
     * Get legal issues for an analysis
     */
    public function getLegalIssues(int $analysisId): JsonResponse
    {
        try {
            $user = Auth::user();
            $analysis = DocumentAnalysis::where('id', $analysisId)
                ->where('user_id', $user->id)
                ->first();

            if (!$analysis) {
                return response()->json([
                    'success' => false,
                    'message' => 'Analysis not found or access denied'
                ], 404);
            }

            $issues = $analysis->legalIssues()
                ->orderBy('severity', 'desc')
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($issue) {
                    return [
                        'id' => $issue->id,
                        'type' => $issue->issue_type,
                        'severity' => $issue->severity,
                        'category' => $issue->category,
                        'title' => $issue->title,
                        'description' => $issue->description,
                        'location' => $issue->formatted_location,
                        'suggested_fix' => $issue->suggested_fix,
                        'legal_reasoning' => $issue->legal_reasoning,
                        'confidence_score' => $issue->confidence_score,
                        'is_resolved' => $issue->is_resolved,
                        'severity_color' => $issue->severity_color,
                        'severity_icon' => $issue->severity_icon,
                        'category_icon' => $issue->category_icon,
                        'created_at' => $issue->created_at
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'issues' => $issues,
                    'summary' => [
                        'total' => $issues->count(),
                        'critical' => $issues->where('severity', 'critical')->count(),
                        'high' => $issues->where('severity', 'high')->count(),
                        'medium' => $issues->where('severity', 'medium')->count(),
                        'low' => $issues->where('severity', 'low')->count(),
                        'resolved' => $issues->where('is_resolved', true)->count()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get legal issues', [
                'user_id' => Auth::id(),
                'analysis_id' => $analysisId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve legal issues'
            ], 500);
        }
    }

    /**
     * Get AI recommendations for an analysis
     */
    public function getRecommendations(int $analysisId): JsonResponse
    {
        try {
            $user = Auth::user();
            $analysis = DocumentAnalysis::where('id', $analysisId)
                ->where('user_id', $user->id)
                ->first();

            if (!$analysis) {
                return response()->json([
                    'success' => false,
                    'message' => 'Analysis not found or access denied'
                ], 404);
            }

            $recommendations = $analysis->aiRecommendations()
                ->orderBy('priority', 'desc')
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($rec) {
                    return [
                        'id' => $rec->id,
                        'type' => $rec->recommendation_type,
                        'priority' => $rec->priority,
                        'title' => $rec->title,
                        'description' => $rec->description,
                        'implementation_guide' => $rec->implementation_guide,
                        'original_text' => $rec->original_text,
                        'suggested_text' => $rec->suggested_text,
                        'location_references' => $rec->formatted_location_references,
                        'legal_rationale' => $rec->legal_rationale,
                        'status' => $rec->status,
                        'impact_score' => $rec->impact_score,
                        'priority_color' => $rec->priority_color,
                        'priority_icon' => $rec->priority_icon,
                        'status_color' => $rec->status_color,
                        'status_icon' => $rec->status_icon,
                        'type_icon' => $rec->type_icon,
                        'created_at' => $rec->created_at
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'recommendations' => $recommendations,
                    'summary' => [
                        'total' => $recommendations->count(),
                        'urgent' => $recommendations->where('priority', 'urgent')->count(),
                        'high' => $recommendations->where('priority', 'high')->count(),
                        'medium' => $recommendations->where('priority', 'medium')->count(),
                        'low' => $recommendations->where('priority', 'low')->count(),
                        'pending' => $recommendations->where('status', 'pending')->count(),
                        'accepted' => $recommendations->where('status', 'accepted')->count(),
                        'implemented' => $recommendations->where('status', 'implemented')->count()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get AI recommendations', [
                'user_id' => Auth::id(),
                'analysis_id' => $analysisId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve recommendations'
            ], 500);
        }
    }

    /**
     * Update recommendation status
     */
    public function updateRecommendationStatus(Request $request, int $recommendationId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|string|in:pending,accepted,rejected,implemented'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $recommendation = AiRecommendation::whereHas('documentAnalysis', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->where('id', $recommendationId)->first();

            if (!$recommendation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Recommendation not found or access denied'
                ], 404);
            }

            $status = $request->input('status');

            switch ($status) {
                case 'accepted':
                    $recommendation->accept();
                    break;
                case 'rejected':
                    $recommendation->reject();
                    break;
                case 'implemented':
                    $recommendation->markAsImplemented();
                    break;
                default:
                    $recommendation->update(['status' => $status]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Recommendation status updated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update recommendation status', [
                'user_id' => Auth::id(),
                'recommendation_id' => $recommendationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update recommendation status'
            ], 500);
        }
    }

    /**
     * Format analysis response
     */
    protected function formatAnalysisResponse(DocumentAnalysis $analysis): array
    {
        return [
            'id' => $analysis->id,
            'document_type' => $analysis->document_type,
            'status' => $analysis->analysis_status,
            'total_issues' => $analysis->total_issues_found,
            'total_recommendations' => $analysis->total_recommendations,
            'confidence_score' => $analysis->confidence_score,
            'processing_time' => $analysis->processing_time_seconds,
            'high_severity_issues' => $analysis->high_severity_issues_count,
            'pending_recommendations' => $analysis->pending_recommendations_count,
            'document_structure' => $analysis->document_structure,
            'legal_entities' => $analysis->legal_entities,
            'analysis_metadata' => $analysis->analysis_metadata,
            'created_at' => $analysis->created_at,
            'updated_at' => $analysis->updated_at
        ];
    }

    /**
     * Get document model class based on type
     */
    protected function getDocumentModel(string $documentType): string
    {
        return match ($documentType) {
            'contract' => \App\Models\Contract::class,
            'unsigned_contract' => \App\Models\UnsignedContract::class,
            'signed_contract' => \App\Models\SignedContract::class,
            default => throw new \InvalidArgumentException("Invalid document type: {$documentType}")
        };
    }
}
