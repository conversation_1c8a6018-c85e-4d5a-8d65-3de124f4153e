<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LegalIssue extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'document_analysis_id',
        'issue_type',
        'severity',
        'category',
        'title',
        'description',
        'location_in_document',
        'suggested_fix',
        'legal_reasoning',
        'confidence_score',
        'is_resolved',
        'resolved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'location_in_document' => 'array',
        'confidence_score' => 'decimal:2',
        'is_resolved' => 'boolean',
        'resolved_at' => 'datetime',
    ];

    /**
     * Get the document analysis that owns this legal issue.
     */
    public function documentAnalysis(): BelongsTo
    {
        return $this->belongsTo(DocumentAnalysis::class);
    }

    /**
     * Get the AI recommendations for this legal issue.
     */
    public function aiRecommendations(): HasMany
    {
        return $this->hasMany(AiRecommendation::class);
    }

    /**
     * Scope to filter by severity.
     */
    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to filter by issue type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('issue_type', $type);
    }

    /**
     * Scope to get unresolved issues.
     */
    public function scopeUnresolved($query)
    {
        return $query->where('is_resolved', false);
    }

    /**
     * Scope to get resolved issues.
     */
    public function scopeResolved($query)
    {
        return $query->where('is_resolved', true);
    }

    /**
     * Scope to get high priority issues.
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('severity', ['high', 'critical']);
    }

    /**
     * Mark the issue as resolved.
     */
    public function markAsResolved(): bool
    {
        return $this->update([
            'is_resolved' => true,
            'resolved_at' => now(),
        ]);
    }

    /**
     * Mark the issue as unresolved.
     */
    public function markAsUnresolved(): bool
    {
        return $this->update([
            'is_resolved' => false,
            'resolved_at' => null,
        ]);
    }

    /**
     * Get the severity color for UI display.
     */
    public function getSeverityColorAttribute(): string
    {
        return match ($this->severity) {
            'low' => '#10b981',      // green
            'medium' => '#f59e0b',   // yellow
            'high' => '#f97316',     // orange
            'critical' => '#ef4444', // red
            default => '#6b7280',    // gray
        };
    }

    /**
     * Get the severity icon for UI display.
     */
    public function getSeverityIconAttribute(): string
    {
        return match ($this->severity) {
            'low' => 'fa-info-circle',
            'medium' => 'fa-exclamation-triangle',
            'high' => 'fa-exclamation-circle',
            'critical' => 'fa-times-circle',
            default => 'fa-question-circle',
        };
    }

    /**
     * Get the category icon for UI display.
     */
    public function getCategoryIconAttribute(): string
    {
        return match ($this->category) {
            'structural' => 'fa-building',
            'content' => 'fa-file-text',
            'risk' => 'fa-shield-alt',
            'compliance' => 'fa-gavel',
            default => 'fa-file',
        };
    }

    /**
     * Check if the issue is critical.
     */
    public function isCritical(): bool
    {
        return $this->severity === 'critical';
    }

    /**
     * Check if the issue is high severity.
     */
    public function isHighSeverity(): bool
    {
        return in_array($this->severity, ['high', 'critical']);
    }

    /**
     * Get formatted location text for display.
     */
    public function getFormattedLocationAttribute(): string
    {
        if (!$this->location_in_document) {
            return 'Location not specified';
        }

        $location = $this->location_in_document;
        $parts = [];

        if (isset($location['page'])) {
            $parts[] = "Page {$location['page']}";
        }

        if (isset($location['section'])) {
            $parts[] = "Section {$location['section']}";
        }

        if (isset($location['paragraph'])) {
            $parts[] = "Paragraph {$location['paragraph']}";
        }

        return implode(', ', $parts) ?: 'Document location';
    }
}
