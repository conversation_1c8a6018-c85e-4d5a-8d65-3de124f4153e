<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Document Analysis Results Table
        Schema::create('document_analyses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('contract_id')->nullable();
            $table->unsignedBigInteger('unsigned_contract_id')->nullable();
            $table->unsignedBigInteger('signed_contract_id')->nullable();
            $table->string('document_type'); // 'contract', 'unsigned_contract', 'signed_contract'
            $table->enum('analysis_status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->longText('content_extracted')->nullable();
            $table->json('document_structure')->nullable(); // Sections, clauses, etc.
            $table->json('legal_entities')->nullable(); // Parties, dates, amounts, etc.
            $table->integer('total_issues_found')->default(0);
            $table->integer('total_recommendations')->default(0);
            $table->decimal('confidence_score', 5, 2)->nullable(); // 0.00 to 100.00
            $table->integer('processing_time_seconds')->nullable();
            $table->json('analysis_metadata')->nullable(); // Additional analysis data
            $table->timestamps();

            $table->index(['user_id', 'analysis_status']);
            $table->index(['document_type', 'analysis_status']);
        });

        // Legal Issues Detected Table
        Schema::create('legal_issues', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('document_analysis_id');
            $table->string('issue_type'); // 'missing_clause', 'ambiguous_language', 'unfavorable_terms', etc.
            $table->enum('severity', ['low', 'medium', 'high', 'critical']);
            $table->string('category'); // 'structural', 'content', 'risk', 'compliance'
            $table->string('title');
            $table->text('description');
            $table->json('location_in_document')->nullable(); // Page, section, paragraph references
            $table->text('suggested_fix')->nullable();
            $table->text('legal_reasoning')->nullable();
            $table->decimal('confidence_score', 5, 2)->nullable();
            $table->boolean('is_resolved')->default(false);
            $table->timestamp('resolved_at')->nullable();
            $table->timestamps();

            $table->index(['document_analysis_id', 'severity']);
            $table->index(['issue_type', 'severity']);
        });

        // AI Recommendations Table
        Schema::create('ai_recommendations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('document_analysis_id');
            $table->unsignedBigInteger('legal_issue_id')->nullable();
            $table->string('recommendation_type'); // 'clause_addition', 'language_modification', 'risk_mitigation', etc.
            $table->enum('priority', ['low', 'medium', 'high', 'urgent']);
            $table->string('title');
            $table->text('description');
            $table->longText('implementation_guide')->nullable();
            $table->text('original_text')->nullable(); // Text to be replaced
            $table->text('suggested_text')->nullable(); // Replacement text
            $table->json('location_references')->nullable(); // Where to implement
            $table->text('legal_rationale')->nullable();
            $table->enum('status', ['pending', 'accepted', 'rejected', 'implemented'])->default('pending');
            $table->decimal('impact_score', 5, 2)->nullable(); // Potential impact rating
            $table->timestamp('implemented_at')->nullable();
            $table->timestamps();

            $table->index(['document_analysis_id', 'priority']);
            $table->index(['recommendation_type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_recommendations');
        Schema::dropIfExists('legal_issues');
        Schema::dropIfExists('document_analyses');
    }
};
