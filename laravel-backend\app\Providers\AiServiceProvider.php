<?php

namespace App\Providers;

use App\Services\OpenAiService;
use App\Services\AiDocumentAnalysisService;
use App\Services\AiChatbotService;
use App\Services\DocumentParsingService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

class AiServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register OpenAI Service
        $this->app->singleton(OpenAiService::class, function ($app) {
            return new OpenAiService();
        });

        // Register Document Parsing Service
        $this->app->singleton(DocumentParsingService::class, function ($app) {
            return new DocumentParsingService();
        });

        // Register AI Document Analysis Service
        $this->app->singleton(AiDocumentAnalysisService::class, function ($app) {
            return new AiDocumentAnalysisService(
                $app->make(OpenAiService::class),
                $app->make(DocumentParsingService::class)
            );
        });

        // Register AI Chatbot Service
        $this->app->singleton(AiChatbotService::class, function ($app) {
            return new AiChatbotService(
                $app->make(OpenAiService::class)
            );
        });

        // Register AI configuration
        $this->app->singleton('ai.config', function ($app) {
            return [
                'enabled' => config('ai.enabled', true),
                'analysis_enabled' => config('ai.analysis_enabled', true),
                'chatbot_enabled' => config('ai.chatbot_enabled', true),
                'extraction_enabled' => config('ai.extraction_enabled', true),
                'async_processing' => config('ai.analysis_async', true),
                'max_document_size' => config('ai.max_document_size', 10485760),
                'content_max_length' => config('ai.content_max_length', 50000),
            ];
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Validate AI configuration on boot
        $this->validateAiConfiguration();

        // Log AI service status
        $this->logAiServiceStatus();
    }

    /**
     * Validate AI configuration
     */
    protected function validateAiConfiguration(): void
    {
        $openaiKey = config('services.openai.api_key');
        
        if (empty($openaiKey)) {
            Log::warning('OpenAI API key is not configured. AI features will be disabled.');
        }

        if (!config('ai.enabled', true)) {
            Log::info('AI features are disabled in configuration.');
        }
    }

    /**
     * Log AI service status
     */
    protected function logAiServiceStatus(): void
    {
        if (config('app.debug')) {
            $status = [
                'ai_enabled' => config('ai.enabled', true),
                'analysis_enabled' => config('ai.analysis_enabled', true),
                'chatbot_enabled' => config('ai.chatbot_enabled', true),
                'extraction_enabled' => config('ai.extraction_enabled', true),
                'async_processing' => config('ai.analysis_async', true),
                'openai_configured' => !empty(config('services.openai.api_key')),
                'model' => config('services.openai.model', 'gpt-4'),
            ];

            Log::debug('AI Service Provider initialized', $status);
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            OpenAiService::class,
            AiDocumentAnalysisService::class,
            AiChatbotService::class,
            DocumentParsingService::class,
            'ai.config',
        ];
    }
}
