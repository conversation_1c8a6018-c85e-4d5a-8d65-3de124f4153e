/* Variables */
:host {
  --card-padding: 25px;
  --border-radius: 12px;
  --transition-speed: 0.3s;
  --primary: #4ECDC4;
  --primary-light: #6CDED6;
  --primary-dark: #3DACA4;
  --primary-gradient: linear-gradient(90deg, var(--primary), var(--primary-light));
  --success: #22c55e;
  --error: #ef4444;
  --spinner-size: 40px;
  --spinner-border: 4px;
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;

  /* Theme-adaptive variables - default to light mode */
  --dashboard-bg: var(--background, #f8fafc);
  --dashboard-section-bg: var(--background, #f8fafc);
  --dashboard-card-bg: var(--surface, #ffffff);
  --dashboard-text-primary: var(--text-primary, #1e293b);
  --dashboard-text-secondary: var(--text-secondary, #64748b);
  --dashboard-text-tertiary: #94a3b8;
  --dashboard-text-heading: var(--text-primary, #1e293b);
  --dashboard-border-color: rgba(0, 0, 0, 0.1);
  --dashboard-shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
  --dashboard-shadow-md: 0 4px 10px rgba(78, 205, 196, 0.2);
  --dashboard-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
  --dashboard-bg-light: #f1f5f9;
  --dashboard-modal-overlay: rgba(0, 0, 0, 0.5);
  --dashboard-secondary-btn-bg: #e2e8f0;
  --dashboard-secondary-btn-hover: #cbd5e1;
  --dashboard-container-bg: var(--surface, #ffffff);
  --dashboard-chart-bg: rgba(0, 0, 0, 0.02);
  --success-bg: rgba(34, 197, 94, 0.1);
  --error-bg: rgba(220, 38, 38, 0.1);
}

/* Dark Mode */
body.dark-mode :host {
  --dashboard-bg: #121212;
  --dashboard-section-bg: #1a1a1a;
  --dashboard-card-bg: #2a2a2a;
  --dashboard-text-primary: #ffffff;
  --dashboard-text-secondary: #cccccc;
  --dashboard-text-tertiary: #666666;
  --dashboard-text-heading: #ffffff;
  --dashboard-border-color: #2e2e2e;
  --dashboard-shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.1);
  --dashboard-shadow-md: 0 4px 10px rgba(78, 205, 196, 0.3);
  --dashboard-shadow-lg: 0 15px 40px rgba(0, 0, 0, 0.4);
  --dashboard-bg-light: #2a2a2a;
  --dashboard-secondary-btn-bg: #444444;
  --dashboard-secondary-btn-hover: #555555;
  --dashboard-container-bg: #222222;
  --dashboard-chart-bg: rgba(255, 255, 255, 0.02);
}

/* Header Styles */
.dashboard-section {
  padding: 2rem 1rem;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--dashboard-section-bg);
  min-height: 100vh;
}

.feature-content {
  width: 100%;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-top: 80px;
}

.section-header h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  width: 100%;
  white-space: nowrap;
  overflow: visible;
  color: var(--dashboard-text-primary);
  font-weight: bold;
  text-align: center;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--dashboard-text-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.highlight {
  color: var(--primary);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  display: inline-block;
}

/* Animation classes */
.animate__fadeInDown {
  animation: fadeInDown 1s ease forwards;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Styles généraux */
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: var(--dashboard-container-bg);
  color: var(--dashboard-text-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--dashboard-shadow-lg);
  max-width: 1200px;
  margin: 0 auto;
}

/* Refresh Button Container */
.refresh-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.btn-refresh {
  background-color: var(--dashboard-card-bg);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--dashboard-shadow-sm);
}

.btn-refresh:hover {
  background-color: var(--primary);
  color: white;
  transform: rotate(180deg);
}

/* Metrics and Cards */
.metrics-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background-color: var(--dashboard-card-bg);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--dashboard-shadow-sm);
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid var(--dashboard-border-color);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--dashboard-shadow-md);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.metric-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--dashboard-text-secondary);
}

.metric-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
}

.metric-icon.total {
  background-color: rgba(79, 70, 229, 0.15);
  color: #4f46e5;
}

.metric-icon.active,
.metric-icon.pending {
  background-color: rgba(245, 158, 11, 0.15);
  color: #f59e0b;
}

.metric-icon.signed {
  background-color: rgba(16, 185, 129, 0.15);
  color: #10b981;
}

.metric-icon.unsigned {
  background-color: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
}

.metric-icon.delayed {
  background-color: rgba(239, 68, 68, 0.15);
  color: #ef4444;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--dashboard-text-heading);
}

.trend {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

/* Chart Containers */
.charts-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.chart-card {
  background-color: var(--dashboard-card-bg);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--dashboard-shadow-sm);
  height: 350px; /* Increased height for better visibility */
  overflow: hidden; /* Prevent overflow */
  transition: all 0.3s ease;
  border: 1px solid var(--dashboard-border-color);
}

.chart-card:hover {
  box-shadow: var(--dashboard-shadow-md);
  transform: translateY(-2px);
}

/* Special styling for the activity chart */
.activity-chart-card {
  background: linear-gradient(to bottom right, var(--dashboard-card-bg), rgba(78, 205, 196, 0.05));
  border: 1px solid rgba(78, 205, 196, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dashboard-text-heading);
  margin-bottom: 0.25rem;
}

.chart-subtitle {
  font-size: 0.8rem;
  color: var(--dashboard-text-tertiary);
  margin-bottom: 0.5rem;
}

.chart-canvas-container {
  height: 280px; /* Increased height for better visibility */
  position: relative;
  width: 100%; /* Ensure full width */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  background-color: var(--dashboard-chart-bg);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.02);
}

/* Add a subtle animation for the activity chart */
.activity-chart-card .chart-canvas-container {
  animation: pulse 3s infinite alternate;
}

@keyframes pulse {
  0% {
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.02);
  }
  100% {
    box-shadow: inset 0 0 15px rgba(59, 130, 246, 0.1);
  }
}

/* Ensure canvas takes full size of container */
.chart-canvas-container canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Widgets Row */
.widgets-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.widget-card {
  background-color: var(--dashboard-card-bg);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--dashboard-shadow-sm);
  border: 1px solid var(--dashboard-border-color);
  transition: all 0.3s ease;
}

.widget-card:hover {
  box-shadow: var(--dashboard-shadow-md);
  transform: translateY(-2px);
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.widget-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--dashboard-text-heading);
}

.widget-action {
  font-size: 0.875rem;
  color: var(--primary-color);
  cursor: pointer;
}

.activity-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.activity-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--dashboard-border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-content {
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--dashboard-text-tertiary);
}

/* Contracts Table */
.contracts-card {
  background-color: var(--dashboard-card-bg);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--dashboard-shadow-sm);
  overflow-x: auto;
  border: 1px solid var(--dashboard-border-color);
  transition: all 0.3s ease;
}

.contracts-card:hover {
  box-shadow: var(--dashboard-shadow-md);
  transform: translateY(-2px);
}

.contracts-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.contracts-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Contract Tabs */
.contract-tabs {
  display: flex;
  border-bottom: 1px solid var(--dashboard-border-color);
  margin-bottom: 1rem;
}

.tab-btn {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--dashboard-text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: var(--dashboard-text-heading);
}

.tab-btn.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

.contracts-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--dashboard-text-heading);
}

.contracts-actions {
  display: flex;
  gap: 0.75rem;
}

.contracts-table {
  width: 100%;
  border-collapse: collapse;
}

.contracts-table th {
  text-align: left;
  padding: 0.75rem 1rem;
  font-weight: 500;
  color: var(--dashboard-text-secondary);
  border-bottom: 1px solid var(--dashboard-border-color);
}

.contracts-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--dashboard-border-color);
  vertical-align: middle;
}

.contracts-table tr:last-child td {
  border-bottom: none;
}

.contract-status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(245, 158, 11, 0.15);
  color: #f59e0b;
}

.status-signed {
  background-color: rgba(16, 185, 129, 0.15);
  color: #10b981;
}

.status-unsigned {
  background-color: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
}

.status-delayed {
  background-color: rgba(239, 68, 68, 0.15);
  color: #ef4444;
}

/* Contract Type Badges */
.contract-type {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.type-signed {
  background-color: rgba(16, 185, 129, 0.15);
  color: #10b981;
}

.type-unsigned {
  background-color: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
}

/* No Data Message */
.no-data {
  text-align: center;
  padding: 2rem;
  color: var(--dashboard-text-tertiary);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
}

.empty-icon {
  font-size: 3rem;
  color: var(--dashboard-border-color);
  margin-bottom: 1rem;
}

.empty-state p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  color: var(--dashboard-text-secondary);
}

.empty-state .btn-add {
  padding: 0.75rem 1.5rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: var(--dashboard-bg-light);
  border-radius: 9999px;
  overflow: hidden;
}

.progress-value {
  height: 100%;
  border-radius: 9999px;
}

.action-btn {
  background: transparent;
  border: none;
  color: var(--dashboard-text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background-color: var(--dashboard-bg-light);
  color: var(--primary);
}

/* Buttons */
.btn-add, .btn-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-add:hover, .btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--dashboard-secondary-btn-bg);
  color: var(--dashboard-text-primary);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: var(--dashboard-secondary-btn-hover);
  transform: translateY(-2px);
  box-shadow: var(--dashboard-shadow-md);
}

.btn-success {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--success);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-success:hover {
  background-color: #1ca350;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--error);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-danger:hover {
  background-color: #d32f2f;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Search Bar */
.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--dashboard-bg-light);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--dashboard-border-color);
}

.search-bar input {
  border: none;
  background: transparent;
  outline: none;
  padding: 0;
  color: var(--dashboard-text-primary);
  width: 200px;
}

.search-bar input::placeholder {
  color: var(--dashboard-text-tertiary);
}

/* Document Analysis Widget Styles */
.analysis-widget-card {
  min-height: 350px;
}

.analysis-widget-content {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.analysis-summary {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

.analysis-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-item {
  text-align: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-light-gray);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.recent-analysis {
  flex: 1;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.recent-analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.recent-analysis-header h4 {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-white);
}

.analysis-status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.analysis-status.status-completed {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.analysis-status.status-processing {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.analysis-status.status-pending {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

.analysis-status.status-failed {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.recent-analysis-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.recent-analysis-details p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-light-gray);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recent-analysis-details i {
  color: var(--primary);
  width: 12px;
}

.analysis-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
}

.no-analysis-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  padding: 2rem 1rem;
}

.empty-state-icon {
  margin-bottom: 1rem;
  color: var(--primary);
  opacity: 0.7;
}

.no-analysis-state h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-white);
  font-size: 1.1rem;
}

.no-analysis-state p {
  margin: 0 0 1.5rem 0;
  color: var(--text-light-gray);
  font-size: 0.9rem;
  line-height: 1.4;
}

.btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  border-radius: 4px;
}

.btn-outline-primary {
  background: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.btn-outline-primary:hover {
  background: var(--primary);
  color: var(--text-white);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .dashboard-actions {
    width: 100%;
    justify-content: space-between;
  }

  .charts-row, .widgets-row {
    grid-template-columns: 1fr;
  }

  .chart-card {
    height: 300px; /* Slightly smaller on mobile */
  }

  .chart-canvas-container {
    height: 230px; /* Slightly smaller on mobile */
  }

  .search-bar input {
    width: 100%;
  }

  .contracts-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .contracts-actions {
    width: 100%;
  }

  /* Improve mobile layout for metrics */
  .metrics-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .metric-card {
    padding: 1rem;
  }

  .metric-value {
    font-size: 1.5rem;
  }

  /* Analysis widget mobile adjustments */
  .analysis-stats {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .analysis-actions {
    flex-direction: column;
  }

  .btn-sm {
    width: 100%;
    justify-content: center;
  }
}

/* Dark and Light Theme Variables - These are now handled in the :host and body.dark-mode :host blocks at the top of this file */

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--dashboard-modal-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: var(--dashboard-text-primary);
}

.spinner {
  width: var(--spinner-size);
  height: var(--spinner-size);
  border: var(--spinner-border) solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: var(--spinner-border) solid white;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Refresh Button */
.btn-refresh {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dashboard-text-secondary);
  border: 1px solid var(--dashboard-border-color);
  width: 36px;
  height: 36px;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 0.75rem;
}

.btn-refresh:hover {
  background-color: var(--dashboard-bg-light);
  color: var(--primary);
}

.btn-refresh i {
  font-size: 0.875rem;
}

/* Export Button */
.btn-export {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 0.75rem;
}

.btn-export:hover {
  background-color: var(--bg-light);
  color: var(--primary-color);
}

/* Sortable Headers */
.sortable-header {
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-right: 1.5rem !important;
}

.sortable-header i {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  opacity: 0.5;
}

.sortable-header:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.sortable-header i.fa-sort-up,
.sortable-header i.fa-sort-down {
  opacity: 1;
  color: var(--primary-color);
}

/* Table Actions */
.table-actions {
  display: flex;
  gap: 0.5rem;
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1.5rem;
  gap: 0.5rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--bg-light);
  color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  margin: 0 0.75rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
  backdrop-filter: blur(5px);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-content {
  background-color: #1a1a1a;
  border-radius: 0.75rem;
  width: 90%;
  max-width: 650px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #333333;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #333333;
  background: linear-gradient(to right, rgba(78, 205, 196, 0.7), rgba(78, 205, 196, 0.1));
  color: white;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-heading);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-title::before {
  content: '';
  display: block;
  width: 6px;
  height: 24px;
  background-color: var(--primary);
  border-radius: 3px;
}

/* Modal body styles are defined below */

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color);
}

.detail-group {
  margin-bottom: 2rem;
}

.modal-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #4ecdce;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(78, 205, 196, 0.4);
  text-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
}

.detail-row {
  display: flex;
  margin-bottom: 1rem;
}

.detail-label {
  width: 40%;
  font-weight: 600;
  color: #4ecdce;
  padding-right: 1rem;
}

.detail-value {
  width: 60%;
  color: #ffffff;
  font-weight: 500;
}

.detail-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-text {
  font-size: 0.875rem;
  color: var(--text-light-gray);
}

.modal-close {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.modal-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-heading);
  transform: rotate(90deg);
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modal-section {
  margin-bottom: 1.5rem;
}

.modal-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-heading);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.25rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  transition: all 0.2s;
  border: 1px solid rgba(78, 205, 196, 0.2);
  margin-bottom: 1rem;
}

.detail-group:hover {
  background-color: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.15);
}

.detail-row {
  display: flex;
  flex-direction: row;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 0.25rem;
}

.detail-label {
  width: 40%;
  font-weight: 600;
  color: #4ecdce;
  padding-right: 1rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  width: 60%;
  color: #ffffff;
  font-weight: 500;
  font-size: 1rem;
  word-break: break-word;
}

.detail-value.empty {
  color: var(--text-tertiary);
  font-style: italic;
}

.detail-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: 500;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.detail-badge i {
  font-size: 1rem;
}

.progress-container {
  margin-top: 0.5rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: var(--bg-light);
  border-radius: 9999px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress-value {
  height: 100%;
  border-radius: 9999px;
}

.progress-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  justify-content: flex-end;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.02);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 5px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  background-color: var(--primary-color-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

.btn-primary i {
  font-size: 1rem;
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background-color: var(--bg-light);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: none;
}

.btn-secondary i {
  font-size: 1rem;
}

.btn-danger {
  background-color: var(--error);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  background-color: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
}

.btn-danger:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(239, 68, 68, 0.3);
}

.btn-danger i {
  font-size: 1rem;
}

/* Print Styles */
@media print {
  .dashboard-container {
    padding: 0;
    background-color: white;
  }

  .dashboard-header, .charts-row, .dashboard-actions, .contracts-actions, .contract-tabs, .pagination, .table-actions {
    display: none !important;
  }

  .metric-card, .contracts-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
    break-inside: avoid;
  }

  .contracts-table th, .contracts-table td {
    padding: 0.5rem;
  }

  .metric-value {
    font-size: 1.5rem;
  }

  .trend {
    display: none;
  }

  .footer {
    display: none;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-light: #1f2937;
    --bg-card: #111827;
    --text-primary: #e5e7eb;
    --text-secondary: #9ca3af;
    --text-tertiary: #6b7280;
    --text-heading: #f9fafb;
    --primary-color: #3b82f6;
    --primary-color-dark: #2563eb;
    --border-color: #374151;
  }
}
