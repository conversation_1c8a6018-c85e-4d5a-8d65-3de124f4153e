<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class OpenAiService
{
    protected $apiKey;
    protected $baseUrl;
    protected $model;
    protected $maxTokens;
    protected $temperature;

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key');
        $this->baseUrl = config('services.openai.base_url', 'https://api.openai.com/v1');
        $this->model = config('services.openai.model', 'gpt-4');
        $this->maxTokens = config('services.openai.max_tokens', 4000);
        $this->temperature = config('services.openai.temperature', 0.3);
    }

    /**
     * Analyze document content using OpenAI
     */
    public function analyzeDocument(string $prompt): string
    {
        Log::info("Starting OpenAI document analysis");

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(120)->post($this->baseUrl . '/chat/completions', [
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $this->getSystemPrompt()
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => $this->maxTokens,
                'temperature' => $this->temperature,
                'response_format' => ['type' => 'json_object']
            ]);

            if (!$response->successful()) {
                throw new Exception("OpenAI API request failed: " . $response->body());
            }

            $data = $response->json();
            $content = $data['choices'][0]['message']['content'] ?? '';

            Log::info("OpenAI document analysis completed", [
                'tokens_used' => $data['usage']['total_tokens'] ?? 0
            ]);

            return $content;

        } catch (Exception $e) {
            Log::error("OpenAI document analysis failed", [
                'error' => $e->getMessage()
            ]);

            // Return fallback response
            return $this->getFallbackAnalysisResponse();
        }
    }

    /**
     * Generate chat response using OpenAI
     */
    public function generateChatResponse(string $userMessage, array $context = []): string
    {
        Log::info("Generating OpenAI chat response");

        try {
            $messages = $this->buildChatMessages($userMessage, $context);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/chat/completions', [
                'model' => $this->model,
                'messages' => $messages,
                'max_tokens' => 1500,
                'temperature' => 0.7,
                'stream' => false
            ]);

            if (!$response->successful()) {
                throw new Exception("OpenAI API request failed: " . $response->body());
            }

            $data = $response->json();
            $content = $data['choices'][0]['message']['content'] ?? '';

            Log::info("OpenAI chat response generated", [
                'tokens_used' => $data['usage']['total_tokens'] ?? 0
            ]);

            return $content;

        } catch (Exception $e) {
            Log::error("OpenAI chat response generation failed", [
                'error' => $e->getMessage()
            ]);

            return "I apologize, but I'm experiencing technical difficulties right now. Please try again in a moment, or contact support if the issue persists.";
        }
    }

    /**
     * Get system prompt for document analysis
     */
    protected function getSystemPrompt(): string
    {
        return "You are an expert legal AI assistant specializing in contract analysis. Your role is to:

1. Identify potential legal issues in contracts including:
   - Missing essential clauses (termination, liability, dispute resolution, confidentiality, etc.)
   - Ambiguous or unclear language that could lead to disputes
   - Unfavorable terms that may disadvantage one party
   - Compliance issues with relevant laws and regulations
   - Inconsistent or contradictory provisions

2. Provide specific, actionable recommendations for improvement including:
   - Suggested clause additions or modifications
   - Language clarifications
   - Risk mitigation strategies
   - Industry best practices

3. Always respond in valid JSON format with the following structure:
{
  \"issues\": [
    {
      \"issue_type\": \"string\",
      \"severity\": \"low|medium|high|critical\",
      \"category\": \"structural|content|risk|compliance\",
      \"title\": \"string\",
      \"description\": \"string\",
      \"location\": {\"section\": \"string\", \"paragraph\": \"number\"},
      \"suggested_fix\": \"string\",
      \"legal_reasoning\": \"string\",
      \"confidence_score\": number
    }
  ],
  \"recommendations\": [
    {
      \"recommendation_type\": \"string\",
      \"priority\": \"low|medium|high|urgent\",
      \"title\": \"string\",
      \"description\": \"string\",
      \"implementation_guide\": \"string\",
      \"original_text\": \"string\",
      \"suggested_text\": \"string\",
      \"location_references\": [{\"section\": \"string\", \"clause\": \"string\"}],
      \"legal_rationale\": \"string\",
      \"impact_score\": number
    }
  ],
  \"confidence_score\": number,
  \"metadata\": {
    \"analysis_type\": \"comprehensive\",
    \"document_complexity\": \"low|medium|high\",
    \"jurisdiction_notes\": \"string\"
  }
}

Be thorough but practical in your analysis. Focus on issues that could have real legal or business impact.";
    }

    /**
     * Build chat messages array with context
     */
    protected function buildChatMessages(string $userMessage, array $context): array
    {
        $messages = [
            [
                'role' => 'system',
                'content' => $this->getChatSystemPrompt()
            ]
        ];

        // Add context from document analysis if available
        if (!empty($context['document_analysis'])) {
            $messages[] = [
                'role' => 'system',
                'content' => "Document Context: " . json_encode($context['document_analysis'])
            ];
        }

        // Add conversation history if available
        if (!empty($context['conversation_history'])) {
            foreach ($context['conversation_history'] as $msg) {
                $messages[] = [
                    'role' => $msg['sender_type'] === 'user' ? 'user' : 'assistant',
                    'content' => $msg['content']
                ];
            }
        }

        // Add current user message
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage
        ];

        return $messages;
    }

    /**
     * Get system prompt for chat interactions
     */
    protected function getChatSystemPrompt(): string
    {
        return "You are a helpful legal AI assistant specializing in contract law and document analysis. 

Your capabilities include:
- Explaining legal terms and concepts in plain language
- Answering questions about contract provisions and their implications
- Providing guidance on legal best practices
- Helping users understand potential risks and benefits
- Offering suggestions for contract improvements

Guidelines:
- Always provide clear, accurate, and helpful responses
- Explain complex legal concepts in simple terms
- When discussing specific contract provisions, reference the document context if available
- Acknowledge limitations and recommend professional legal advice when appropriate
- Be conversational but professional
- Ask clarifying questions when needed

Remember: You provide educational information and guidance, but users should consult qualified legal professionals for specific legal advice.";
    }

    /**
     * Get fallback analysis response when OpenAI fails
     */
    protected function getFallbackAnalysisResponse(): string
    {
        return json_encode([
            'issues' => [
                [
                    'issue_type' => 'analysis_unavailable',
                    'severity' => 'medium',
                    'category' => 'structural',
                    'title' => 'AI Analysis Temporarily Unavailable',
                    'description' => 'The automated legal analysis service is currently unavailable. Please try again later or consider manual review.',
                    'location' => null,
                    'suggested_fix' => 'Retry analysis or consult with a legal professional for manual review.',
                    'legal_reasoning' => 'Technical service limitation requires alternative analysis approach.',
                    'confidence_score' => 50.0
                ]
            ],
            'recommendations' => [
                [
                    'recommendation_type' => 'manual_review',
                    'priority' => 'medium',
                    'title' => 'Consider Professional Legal Review',
                    'description' => 'While automated analysis is unavailable, consider having this document reviewed by a qualified legal professional.',
                    'implementation_guide' => 'Contact a licensed attorney familiar with contract law in your jurisdiction.',
                    'original_text' => null,
                    'suggested_text' => null,
                    'location_references' => null,
                    'legal_rationale' => 'Professional legal expertise ensures comprehensive document review and compliance.',
                    'impact_score' => 80.0
                ]
            ],
            'confidence_score' => 50.0,
            'metadata' => [
                'analysis_type' => 'fallback',
                'document_complexity' => 'unknown',
                'jurisdiction_notes' => 'Unable to determine due to service limitation'
            ]
        ]);
    }

    /**
     * Check if OpenAI service is available
     */
    public function isServiceAvailable(): bool
    {
        if (!$this->apiKey) {
            return false;
        }

        $cacheKey = 'openai_service_status';
        
        return Cache::remember($cacheKey, 300, function () {
            try {
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $this->apiKey,
                ])->timeout(10)->get($this->baseUrl . '/models');

                return $response->successful();
            } catch (Exception $e) {
                Log::warning("OpenAI service availability check failed", [
                    'error' => $e->getMessage()
                ]);
                return false;
            }
        });
    }

    /**
     * Get service status information
     */
    public function getServiceStatus(): array
    {
        return [
            'available' => $this->isServiceAvailable(),
            'model' => $this->model,
            'max_tokens' => $this->maxTokens,
            'temperature' => $this->temperature,
            'api_configured' => !empty($this->apiKey)
        ];
    }
}
